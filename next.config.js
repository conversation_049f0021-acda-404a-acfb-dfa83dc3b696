/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone output for Docker
  output: 'standalone',

  // NOTE: A TypeScript variant (next.config.ts) also exists. Next.js prefers JS config here.
  // Consolidated settings so Docker build doesn't fail on lint/type issues; we already run separate CI checks.
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Allow image to build even if type errors exist (they're still surfaced in dev/CI)
    ignoreBuildErrors: true,
  },

  // Server external packages
  serverExternalPackages: ['@prisma/client', 'bcryptjs'],
  
  // Image optimization configuration
  images: {
    domains: [
      'localhost',
      'ui-avatars.com',
      'images.unsplash.com',
      'via.placeholder.com',
    ],
    // Disable image optimization for Docker builds to reduce complexity
    unoptimized: process.env.NODE_ENV === 'production',
  },
  
  // Environment variables
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // (Removed custom webpack alias that intercepted next/document – no longer needed)
  
  // Headers configuration
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
  
  // Redirects configuration
  async redirects() {
    return [
      {
        source: '/signup',
        destination: '/auth/register',
        permanent: true,
      },
      {
        source: '/login',
        destination: '/auth/signin',
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
