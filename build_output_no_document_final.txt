
> forum-app@0.1.0 build
> next build

   ▲ Next.js 15.4.5 (Turbopack)
   - Environments: .env.local, .env

   Creating an optimized production build ...
/*! 🌼 daisyUI 5.0.50 */
 ✓ Compiled successfully in 6.0s
   Linting and checking validity of types ...

./src/app/admin/page.tsx
131:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
132:60  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
133:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
139:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
150:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
155:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
159:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
176:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
176:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
193:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
199:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
209:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
217:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
227:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
245:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
259:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
274:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
281:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
291:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
302:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
309:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
326:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
333:61  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
343:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
358:14  Warning: 'e' is defined but never used.  @typescript-eslint/no-unused-vars
372:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
420:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
464:15  Warning: 'updatedUser' is assigned a value but never used.  @typescript-eslint/no-unused-vars
506:43  Warning: 'userName' is defined but never used.  @typescript-eslint/no-unused-vars
514:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
550:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
565:15  Warning: 'result' is assigned a value but never used.  @typescript-eslint/no-unused-vars
617:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
622:55  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
770:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
844:21  Warning: Unused eslint-disable directive (no problems were reported from 'no-await-in-loop').
1254:35  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
1369:21  Warning: Unused eslint-disable directive (no problems were reported from 'no-await-in-loop').
1636:60  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1644:65  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1654:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1662:57  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1784:27  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
1830:27  Warning: 'id' is assigned a value but never used.  @typescript-eslint/no-unused-vars
1876:96  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/comments/route.ts
37:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/communities/route.ts
8:7  Warning: 'updateCommunitySchema' is assigned a value but never used.  @typescript-eslint/no-unused-vars
58:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
207:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/posts/route.ts
8:7  Warning: 'updatePostSchema' is assigned a value but never used.  @typescript-eslint/no-unused-vars
61:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
198:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/reports/route.ts
26:7  Warning: 'updateReportSchema' is assigned a value but never used.  @typescript-eslint/no-unused-vars
65:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/settings/route.ts
119:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/admin/stats/route.ts
7:27  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/admin/users/route.ts
57:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
239:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/comments/route.ts
34:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
200:60  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/communities/route.ts
29:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/followers/route.ts
30:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/notifications/route.ts
43:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/posts/route.ts
34:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/profile/route.ts
17:27  Warning: 'request' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/socket/route.ts
2:30  Warning: 'Socket' is defined but never used.  @typescript-eslint/no-unused-vars
6:11  Warning: 'SocketServer' is defined but never used.  @typescript-eslint/no-unused-vars
10:15  Warning: 'globalThis' is defined but never used.  @typescript-eslint/no-unused-vars
18:27  Warning: 'req' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/api/users/[id]/follow/route.ts
8:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
18:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
20:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
34:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
61:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
99:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
113:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/users/public/route.ts
17:11  Warning: 'isAdmin' is assigned a value but never used.  @typescript-eslint/no-unused-vars
27:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
67:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/api/users/route.ts
39:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
90:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
172:11  Warning: 'hashedPassword' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/app/auth/register/page.tsx
124:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
166:17  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
175:64  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
192:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/auth/signin/page.tsx
38:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
57:14  Warning: 'error' is defined but never used.  @typescript-eslint/no-unused-vars
77:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/communities/[slug]/page.tsx
142:6  Warning: React Hook useEffect has a missing dependency: 'fetchCommunity'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
228:21  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
277:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
382:29  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
498:27  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
575:27  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/communities/create/page.tsx
155:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
181:67  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
363:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/communities/page.tsx
42:7  Warning: 'mockCommunities' is assigned a value but never used.  @typescript-eslint/no-unused-vars
129:7  Warning: 'categories' is assigned a value but never used.  @typescript-eslint/no-unused-vars
179:6  Warning: React Hook useEffect has a missing dependency: 'fetchCommunities'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
188:6  Warning: React Hook useEffect has a missing dependency: 'fetchCommunities'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
388:21  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
420:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/error.tsx
33:54  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
33:97  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/feed/page.tsx
51:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
52:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
140:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
153:31  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
153:52  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities

./src/app/feed/page_new.tsx
51:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
52:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
140:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
153:31  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
153:52  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities

./src/app/followers/page.tsx
41:10  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
89:75  Warning: 'newCount' is defined but never used.  @typescript-eslint/no-unused-vars
213:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
226:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/following/page.tsx
12:11  Warning: 'followingData' is assigned a value but never used.  @typescript-eslint/no-unused-vars
13:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
26:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
40:16  Warning: 'err' is defined but never used.  @typescript-eslint/no-unused-vars
47:53  Warning: 'isFollowing' is defined but never used.  @typescript-eslint/no-unused-vars
53:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
111:84  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
133:29  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
181:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
195:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/layout.tsx
5:8  Warning: 'Navbar' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/loading.tsx
9:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/not-found.tsx
22:33  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/notifications/page.tsx
49:10  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
283:29  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
398:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/page.tsx
46:26  Warning: 'status' is assigned a value but never used.  @typescript-eslint/no-unused-vars
280:29  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
431:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/posts/[id]/page.tsx
65:7  Warning: 'mockPosts' is assigned a value but never used.  @typescript-eslint/no-unused-vars
238:7  Warning: 'mockComments' is assigned a value but never used.  @typescript-eslint/no-unused-vars
341:10  Warning: 'commentsLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
347:9  Warning: 'socketRef' is assigned a value but never used.  @typescript-eslint/no-unused-vars
356:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
360:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
365:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
375:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
422:9  Warning: 'fetchComments' is assigned a value but never used.  @typescript-eslint/no-unused-vars
442:6  Warning: React Hook useEffect has a missing dependency: 'fetchPost'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
501:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
505:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
508:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
561:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
565:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
568:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
635:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
646:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
783:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
877:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
891:21  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
969:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
1029:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
1117:27  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/posts/create/page.tsx
376:13  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
376:16  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
458:29  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
580:29  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
610:29  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
621:49  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/posts/page.tsx
100:6  Warning: React Hook useEffect has a missing dependency: 'fetchPosts'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
109:6  Warning: React Hook useEffect has a missing dependency: 'fetchPosts'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
183:16  Warning: 'err' is defined but never used.  @typescript-eslint/no-unused-vars
500:50  Warning: 'index' is defined but never used.  @typescript-eslint/no-unused-vars
509:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
672:25  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
672:90  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities

./src/app/profile/[id]/page.tsx
72:6  Warning: React Hook useEffect has a missing dependency: 'fetchUser'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
139:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
284:19  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/profile/page.tsx
460:32  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
460:53  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
527:30  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
527:51  Warning: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
555:29  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/settings/page.tsx
43:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
271:99  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
389:103  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/app/users/page.tsx
13:26  Warning: 'toggleFollow' is assigned a value but never used.  @typescript-eslint/no-unused-vars
14:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
32:56  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
201:20  Warning: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
239:64  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
252:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/AnimatedLogo.tsx
251:7  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/FollowButton.tsx
32:98  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
36:43  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
61:6  Warning: React Hook useEffect has missing dependencies: 'followerCount' and 'isFollowing'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps
76:16  Warning: '_' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ImageAttachment.tsx
126:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/ImageGallery.tsx
46:11  Warning: 'remainingCount' is assigned a value but never used.  @typescript-eslint/no-unused-vars
85:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
144:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
187:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/ImageUpload.tsx
95:6  Warning: React Hook useCallback has a missing dependency: 'handleFileSelect'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
170:22  Warning: 'imgW' is assigned a value but never used.  @typescript-eslint/no-unused-vars
170:36  Warning: 'imgH' is assigned a value but never used.  @typescript-eslint/no-unused-vars
241:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
325:21  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/LogoIntro.tsx
100:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
109:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/ProfilePictureUpload.tsx
39:48  Warning: 'preview' is defined but never used.  @typescript-eslint/no-unused-vars
75:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/RealtimeProvider.tsx
46:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
59:6  Warning: React Hook useEffect has a missing dependency: 'session?.user'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
59:7  Warning: React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.  react-hooks/exhaustive-deps
59:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/Sidebar.tsx
32:10  Warning: 'loading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
47:76  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
98:6  Warning: React Hook useEffect has a missing dependency: 'isOpen'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
151:17  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
175:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/UserCard.tsx
66:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
105:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
202:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/navbar.tsx
14:10  Warning: 'isMenuOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
14:22  Warning: 'setIsMenuOpen' is assigned a value but never used.  @typescript-eslint/no-unused-vars
84:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
93:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
102:13  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
147:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
171:19  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
191:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
192:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
194:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/useFollowing.ts
48:41  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
162:35  Warning: 'userId' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/auth.ts
115:31  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
117:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
123:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
123:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
124:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/realtime.ts
5:14  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
6:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
13:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
16:67  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
28:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
34:68  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
   Collecting page data ...
   Generating static pages (0/46) ...
Error: <Html> should not be imported outside of pages/_document.
Read more: https://nextjs.org/docs/messages/no-document-import-in-page
    at x (.next/server/chunks/2341.js:6:1351)
Error occurred prerendering page "/500". Read more: https://nextjs.org/docs/messages/prerender-error
Error: <Html> should not be imported outside of pages/_document.
Read more: https://nextjs.org/docs/messages/no-document-import-in-page
    at x (.next/server/chunks/2341.js:6:1351)
Export encountered an error on /_error: /500, exiting the build.
 ⨯ Next.js build worker exited with code: 1 and signal: null
