#!/usr/bin/env tsx
import 'dotenv/config';
import { promises as fs } from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

type SeedPayload = {
  data: {
    users: any[];
    accounts: any[];
    sessions: any[];
    forumSettings: any[];
    verificationTokens: any[];
    communities: any[];
    communityMembers: any[];
    posts: any[];
    comments: any[];
    likes: any[];
    follows: any[];
    reports: any[];
    notifications: any[];
  };
};

async function readSeedData(customPath?: string): Promise<SeedPayload> {
  const defaultPath = path.resolve('prisma', 'seed-data.json');
  const filePath = customPath ? path.resolve(customPath) : defaultPath;
  const raw = await fs.readFile(filePath, 'utf8');
  return JSON.parse(raw);
}

async function main() {
  const seedPath = process.env.SEED_FILE;
  const reset = process.env.SEED_RESET === 'true';
  console.log(`[prisma:seed] Seeding from ${seedPath || 'prisma/seed-data.json'} (reset=${reset})...`);
  const { data } = await readSeedData(seedPath);

  if (reset) {
    console.log('[prisma:seed] RESET mode: deleting existing data...');
    // Delete in dependency-safe order (children -> parents)
    await prisma.notification.deleteMany();
    await prisma.like.deleteMany();
    await prisma.comment.deleteMany();
    await prisma.post.deleteMany();
    await prisma.communityMember.deleteMany();
    await prisma.community.deleteMany();
    await prisma.follow.deleteMany();
    await prisma.report.deleteMany();
    await prisma.account.deleteMany();
    await prisma.session.deleteMany();
    await prisma.forumSettings.deleteMany();
    await prisma.verificationToken.deleteMany();
    await prisma.user.deleteMany();
    console.log('[prisma:seed] Existing data cleared. Creating fresh records...');

    // Fresh create preserving IDs
    for (const u of data.users) await prisma.user.create({ data: u });
    for (const a of data.accounts) await prisma.account.create({ data: a });
    for (const s of data.sessions) await prisma.session.create({ data: s });
    for (const fsRec of data.forumSettings) await prisma.forumSettings.create({ data: fsRec });
    for (const vt of data.verificationTokens) await prisma.verificationToken.create({ data: vt });
    for (const c of data.communities) await prisma.community.create({ data: c });
    for (const cm of data.communityMembers) await prisma.communityMember.create({ data: cm });
    for (const p of data.posts) await prisma.post.create({ data: p });
    for (const c of data.comments) await prisma.comment.create({ data: c });
    for (const l of data.likes) await prisma.like.create({ data: l });
    for (const f of data.follows) await prisma.follow.create({ data: f });
    for (const r of data.reports) await prisma.report.create({ data: r });
    for (const n of data.notifications) await prisma.notification.create({ data: n });
  } else {
    console.log('[prisma:seed] UPSERT mode: merging records (IDs may differ if already present).');
    // Users (match by unique email to avoid duplicate email errors)
    for (const u of data.users) {
      await prisma.user.upsert({
        where: { email: u.email },
        update: u,
        create: u,
      });
    }
    for (const a of data.accounts) {
      await prisma.account.upsert({
        where: { id: a.id },
        update: a,
        create: a,
      });
    }
    for (const s of data.sessions) {
      await prisma.session.upsert({
        where: { id: s.id },
        update: s,
        create: s,
      });
    }
    for (const fsRec of data.forumSettings) {
      await prisma.forumSettings.upsert({
        where: { id: fsRec.id },
        update: fsRec,
        create: fsRec,
      });
    }
    for (const vt of data.verificationTokens) {
      await prisma.verificationToken.upsert({
        where: { token: vt.token },
        update: vt,
        create: vt,
      });
    }
    for (const c of data.communities) {
      await prisma.community.upsert({
        where: { id: c.id },
        update: c,
        create: c,
      });
    }
    for (const cm of data.communityMembers) {
      await prisma.communityMember.upsert({
        where: { id: cm.id },
        update: cm,
        create: cm,
      });
    }
    for (const p of data.posts) {
      await prisma.post.upsert({
        where: { id: p.id },
        update: p,
        create: p,
      });
    }
    for (const c of data.comments) {
      await prisma.comment.upsert({
        where: { id: c.id },
        update: c,
        create: c,
      });
    }
    for (const l of data.likes) {
      await prisma.like.upsert({
        where: { id: l.id },
        update: l,
        create: l,
      });
    }
    for (const f of data.follows) {
      await prisma.follow.upsert({
        where: { id: f.id },
        update: f,
        create: f,
      });
    }
    for (const r of data.reports) {
      await prisma.report.upsert({
        where: { id: r.id },
        update: r,
        create: r,
      });
    }
    for (const n of data.notifications) {
      await prisma.notification.upsert({
        where: { id: n.id },
        update: n,
        create: n,
      });
    }
  }

  console.log('[prisma:seed] Seeding completed successfully.');
}

main()
  .catch((e) => {
    console.error('[prisma:seed] Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
