// This is your Prisma schema file,
model Follow {
  id          String   @id @default(cuid())
  followerId  String
  followingId String
  createdAt   DateTime @default(now())

  follower    User     @relation("UserFollows", fields: [followerId], references: [id], onDelete: Cascade)
  following   User     @relation("UserFollowedBy", fields: [followingId], references: [id], onDelete: Cascade)

  @@unique([followerId, followingId])
  @@index([followerId])
  @@index([followingId])
}
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  role          Role      @default(USER)
  bio           String?
  password      String?   // For credential-based registration
  isPrivate     Boolean   @default(false) // Privacy setting
  showEmail     Boolean   @default(false) // Email visibility setting
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts Account[]
  sessions Session[]
  posts    Post[]
  comments Comment[]
  likes    Like[]
  communities Community[]
  communityMemberships CommunityMember[]

  // Following relationships
  following  Follow[] @relation("UserFollows")
  followers  Follow[] @relation("UserFollowedBy")

  // Reports
  reports         Report[] @relation("UserReports")
  reportedAgainst Report[] @relation("ReportedUsers")
  handledReports  Report[] @relation("HandledReports")

  // Notifications
  notifications        Notification[] @relation("UserNotifications")
  sentNotifications    Notification[] @relation("NotificationSender")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Post {
  id          String   @id @default(cuid())
  title       String
  content     String   @db.Text
  published   Boolean  @default(false)
  images      String[] @default([])
  videoUrl    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  authorId    String
  communityId String?

  author    User       @relation(fields: [authorId], references: [id], onDelete: Cascade)
  community Community? @relation(fields: [communityId], references: [id], onDelete: SetNull)
  comments  Comment[]
  likes     Like[]
  reports   Report[]
  notifications Notification[]

  @@index([createdAt])
  @@index([published])
  @@index([communityId])
}

model Comment {
  id        String   @id @default(cuid())
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  authorId  String
  postId    String
  parentId  String?

  author   User      @relation(fields: [authorId], references: [id], onDelete: Cascade)
  post     Post      @relation(fields: [postId], references: [id], onDelete: Cascade)
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")
  likes    Like[]
  reports  Report[]
  notifications Notification[]

  @@index([postId])
  @@index([createdAt])
}

model Like {
  id        String   @id @default(cuid())
  userId    String
  postId    String?
  commentId String?
  createdAt DateTime @default(now())

  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  post    Post?    @relation(fields: [postId], references: [id], onDelete: Cascade)
  comment Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@unique([userId, commentId])
}

model Community {
  id          String   @id @default(cuid())
  name        String   @unique
  description String   @db.Text
  slug        String   @unique
  image       String?
  isPrivate   Boolean  @default(false)
  archived    Boolean  @default(false)
  allowPosts  Boolean  @default(true)
  requireApproval Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  creatorId   String

  creator User              @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  posts   Post[]
  members CommunityMember[]

  @@index([createdAt])
  @@index([slug])
}

model CommunityMember {
  id          String   @id @default(cuid())
  userId      String
  communityId String
  role        CommunityRole @default(MEMBER)
  joinedAt    DateTime @default(now())

  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  community Community @relation(fields: [communityId], references: [id], onDelete: Cascade)

  @@unique([userId, communityId])
  @@index([communityId])
}

enum Role {
  USER
  EDITOR
  ADMIN
}

enum CommunityRole {
  MEMBER
  MODERATOR
  ADMIN
}

model Report {
  id          String      @id @default(cuid())
  type        ReportType
  reason      String
  description String?     @db.Text
  status      ReportStatus @default(PENDING)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Reporter
  reporterId  String
  reporter    User        @relation("UserReports", fields: [reporterId], references: [id], onDelete: Cascade)

  // Reported content (one of these will be set)
  postId      String?
  post        Post?       @relation(fields: [postId], references: [id], onDelete: Cascade)
  commentId   String?
  comment     Comment?    @relation(fields: [commentId], references: [id], onDelete: Cascade)
  userId      String?
  user        User?       @relation("ReportedUsers", fields: [userId], references: [id], onDelete: Cascade)

  // Admin who handled the report
  handledById String?
  handledBy   User?       @relation("HandledReports", fields: [handledById], references: [id])
  handledAt   DateTime?

  @@index([status])
  @@index([type])
  @@index([createdAt])
}

enum ReportType {
  POST
  COMMENT
  USER
}

enum ReportStatus {
  PENDING
  REVIEWED
  RESOLVED
  DISMISSED
}

model ForumSettings {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String   @db.Text
  type        SettingType
  category    String
  description String?
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([category])
  @@index([isPublic])
}

enum SettingType {
  STRING
  NUMBER
  BOOLEAN
  JSON
}

model Notification {
  id          String           @id @default(cuid())
  type        NotificationType
  title       String
  message     String           @db.Text
  actionUrl   String?
  read        Boolean          @default(false)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Target user (who receives the notification)
  userId      String
  user        User             @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)

  // Optional: User who triggered the notification
  fromUserId  String?
  fromUser    User?            @relation("NotificationSender", fields: [fromUserId], references: [id], onDelete: SetNull)

  // Optional: Related content
  postId      String?
  post        Post?            @relation(fields: [postId], references: [id], onDelete: Cascade)
  commentId   String?
  comment     Comment?         @relation(fields: [commentId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
  @@index([read])
}

enum NotificationType {
  LIKE
  COMMENT
  FOLLOW
  MENTION
  POST
  COMMUNITY
}
