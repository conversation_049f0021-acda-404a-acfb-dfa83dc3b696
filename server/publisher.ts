// Utility script to publish messages into the Socket.IO cluster via Redis
// Can be used by Next.js API routes (forked process) or cron jobs via `node server/publisher.js`
import Redis from 'ioredis';
import dotenv from 'dotenv';

dotenv.config();

const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const channel = process.env.REALTIME_REDIS_CHANNEL || 'socketio#emitter';

// The Socket.IO Redis adapter listens to messages on specific channels; for custom fan-out we can
// also publish to bespoke channels and have the realtime server subscribe. For now we'll rely on adapter rooms.

export function buildRedis() {
  return new Redis(REDIS_URL, { lazyConnect: false });
}

export async function emitGlobal(event: string, payload: any) {
  // Simplest strategy: connect directly to one realtime server via HTTP in future.
  // For now you might integrate socket.io-emitter package for proper protocol-level emits.
  console.warn('[publisher] emitGlobal invoked without socket.io-emitter; implement as needed', { event });
}

if (require.main === module) {
  // Example usage: node server/publisher.ts post:new '{"id":123}'
  const [,, evt, json] = process.argv;
  if (!evt) {
    console.error('Usage: tsx server/publisher.ts <event> <jsonPayload>');
    process.exit(1);
  }
  let payload: any = {};
  if (json) {
    try { payload = JSON.parse(json); } catch (e) { console.error('Invalid JSON payload'); process.exit(1); }
  }
  emitGlobal(evt, payload).then(() => process.exit(0));
}
