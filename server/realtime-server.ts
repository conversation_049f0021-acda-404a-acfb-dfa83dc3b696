import http from 'http';
import { Server } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import Redis from 'ioredis';
import dotenv from 'dotenv';
import { randomUUID } from 'crypto';

dotenv.config();

const PORT = parseInt(process.env.REALTIME_PORT || '4001', 10);
const HOST = process.env.REALTIME_HOST || '0.0.0.0';
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const REDIS_TLS = process.env.REDIS_TLS === '1';

const instanceId = randomUUID();

function buildRedis(url: string) {
  return new Redis(url, {
    lazyConnect: false,
    tls: REDIS_TLS ? {} : undefined,
    maxRetriesPerRequest: 20,
  });
}

async function bootstrap() {
  const httpServer = http.createServer((req, res) => {
    if (req.url === '/healthz') {
      res.writeHead(200, { 'content-type': 'application/json' });
      res.end(JSON.stringify({ status: 'ok', instanceId }));
      return;
    }
    res.writeHead(404);
    res.end();
  });

  // Derive allowed CORS origins (comma separated). If empty or contains '*', allow all (dev convenience).
  const rawOrigins = process.env.CORS_ORIGIN || '';
  const allowedOrigins = rawOrigins.split(',').map(o => o.trim()).filter(Boolean);
  const corsOrigin: any = (allowedOrigins.length === 0 || allowedOrigins.includes('*')) ? true : allowedOrigins;

  const io = new Server(httpServer, {
    cors: {
      origin: corsOrigin,
      credentials: true,
    },
    serveClient: false,
    transports: ['websocket', 'polling'],
  });

  console.log('[realtime] Allowed CORS origins:', corsOrigin === true ? '* (all)' : allowedOrigins);

  // Redis adapter for horizontal scaling
  let adapterEnabled = false;
  try {
    const pubClient = buildRedis(REDIS_URL);
    const subClient = buildRedis(REDIS_URL); // separate connection per adapter docs

    const attachErrorHandlers = (client: any, label: string) => {
      client.on('error', (err: any) => {
        console.error(`[realtime][redis][${label}]`, err.message);
      });
      client.on('end', () => console.warn(`[realtime][redis][${label}] connection ended`));
      client.on('reconnecting', () => console.warn(`[realtime][redis][${label}] reconnecting`));
    };
    attachErrorHandlers(pubClient, 'pub');
    attachErrorHandlers(subClient, 'sub');

    await Promise.all([
      pubClient.ping().catch(e => { throw e; }),
      subClient.ping().catch(e => { throw e; })
    ]);

    io.adapter(createAdapter(pubClient, subClient));
    adapterEnabled = true;
    console.log('[realtime] Redis adapter enabled');
  } catch (err: any) {
    console.warn('[realtime] Redis unavailable, running without adapter (no horizontal scaling).', err?.message);
  }

  io.use(async (socket, next) => {
    // Basic auth via query token (placeholder; upgrade to JWT validation using NextAuth session token endpoint)
    const token = socket.handshake.auth?.token || socket.handshake.query?.token;
    // For now accept all; you can validate with your session store.
    if (!token && process.env.REALTIME_REQUIRE_TOKEN === '1') {
      return next(new Error('AUTH_REQUIRED'));
    }
    return next();
  });

  io.on('connection', (socket) => {
    console.log(`[realtime] client connected ${socket.id}`);

    socket.on('join', (room: string) => {
      if (room && typeof room === 'string') {
        socket.join(room);
      }
    });

    socket.on('post:typing', (postId: string) => {
      if (postId) socket.to(`post:${postId}`).emit('post:typing', { postId, user: socket.id });
    });

    socket.on('disconnect', (reason) => {
      console.log(`[realtime] client disconnected ${socket.id} reason=${reason}`);
    });
  });

  httpServer.listen(PORT, HOST, () => {
    console.log(`[realtime] server listening on http://${HOST}:${PORT} (instance ${instanceId}) adapter=${adapterEnabled ? 'redis' : 'memory'}`);
  });
}

bootstrap().catch((err) => {
  console.error('[realtime] bootstrap error', err);
  process.exit(1);
});
