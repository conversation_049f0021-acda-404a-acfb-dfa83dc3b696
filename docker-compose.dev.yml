version: '3.8'

# Development database & cache stack
# Usage:
#   Start: docker compose -f docker-compose.dev.yml up -d
#   Stop:  docker compose -f docker-compose.dev.yml down

services:
  dev-postgres:
    image: postgres:15-alpine
    container_name: forum-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: forum_db_dev
      POSTGRES_USER: forum_user
      POSTGRES_PASSWORD: forum_password
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5440:5432"  # host:container
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U forum_user -d forum_db_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - forum-dev-network

  dev-redis:
    image: redis:7-alpine
    container_name: forum-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    ports:
      - "6380:6379"  # host:container
    volumes:
      - redis_dev_data:/data
    networks:
      - forum-dev-network

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  forum-dev-network:
    driver: bridge
