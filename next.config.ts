import type { NextConfig } from "next";

// Simplified Next.js configuration
const config: NextConfig = {
  output: 'standalone',
  // Enable additional React warnings & double-invocation of certain lifecycles in dev;
  // stays inert (no double render) in production but documents intent.
  reactStrictMode: true,

  eslint: {
    ignoreDuringBuilds: true,
  },

  typescript: {
    ignoreBuildErrors: true,
  },

  serverExternalPackages: ['@prisma/client', 'bcryptjs'],

  images: {
    unoptimized: process.env.NODE_ENV === 'production',
    domains: [
      'localhost',
      'ui-avatars.com',
      'images.unsplash.com',
      'via.placeholder.com',
    ],
  },

  async redirects() {
    return [
      { source: '/signup', destination: '/auth/register', permanent: true },
      { source: '/login', destination: '/auth/signin', permanent: true },
    ];
  },
};

export default config;
