// Check if users were created with proper password hashing
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    const users = await prisma.user.findMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>']
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        password: true,
        role: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log('Found users:', users.length);
    
    users.forEach(user => {
      console.log('\n--- User ---');
      console.log('ID:', user.id);
      console.log('Name:', user.name);
      console.log('Email:', user.email);
      console.log('Role:', user.role);
      console.log('Password exists:', !!user.password);
      console.log('Password length:', user.password ? user.password.length : 0);
      console.log('Password starts with $2b$:', user.password ? user.password.startsWith('$2b$') : false);
      console.log('Created:', user.createdAt);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
