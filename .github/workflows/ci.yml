name: CI

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Lint
        run: npm run lint --if-present

      - name: Run tests
        run: npx jest --config jest.config.cjs --runInBand

      - name: Build
        env:
          NEXT_PUBLIC_BASE_URL: http://localhost:3000
        run: npm run build

      - name: Upload coverage (artifact)
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: coverage
          path: coverage/
          if-no-files-found: ignore
