version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: forum-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: forum_db
      POSTGRES_USER: forum_user
      POSTGRES_PASSWORD: forum_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - forum-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U forum_user -d forum_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Forum Application
  forum-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: forum-app
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: "****************************************************/forum_db?schema=public"
      
      # NextAuth.js
      NEXTAUTH_URL: "http://localhost:3002"
      NEXTAUTH_SECRET: "your-super-secret-nextauth-secret-change-in-production"
      
      # Node Environment
      NODE_ENV: "production"
      
      # Optional OAuth (add your own credentials)
      GOOGLE_CLIENT_ID: ""
      GOOGLE_CLIENT_SECRET: ""
      GITHUB_CLIENT_ID: ""
      GITHUB_CLIENT_SECRET: ""
    ports:
      - "3002:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - forum-network
    volumes:
      # Mount uploads directory for file persistence
      - uploads_data:/app/public/uploads

  # Redis for caching (optional, for future use)
  redis:
    image: redis:7-alpine
    container_name: forum-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - forum-network
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local

networks:
  forum-network:
    driver: bridge
