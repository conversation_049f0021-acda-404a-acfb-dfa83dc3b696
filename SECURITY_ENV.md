# Environment & Secrets Policy

Date: 2025-08-27

## Policy Summary
- Never commit real environment files (`.env`, `.env.*`) except example templates (`.env.example`, `.env.prod.example`).
- All previously committed secret-bearing files are treated as compromised; rotate immediately.
- Use `scripts/gen-prod-env.ts` to generate production env artifacts locally or in CI, not manually stored secrets.

## Rotation Checklist
1. Generate new secrets (`openssl rand -base64 48`).
2. Update provider/secret manager values.
3. Redeploy services (ensuring new container/task picks up values).
4. Invalidate old sessions if auth secret rotated.
5. Remove any local caches containing old env values.

## Files Kept
- `.env.example` (development template)
- `.env.prod.example` (production template guidance)
- `scripts/gen-prod-env.ts` (non-secret generator logic)

## Prohibited
- Committing `.env`, `.env.local`, `.env.production`, generated `.env.production` outputs.
- Hardcoding secrets in source (`src/**`).

## Detection
Run prior to commit:
```
grep -R "NEXTAUTH_SECRET=" -n . | grep -v example || true
```
Add a pre-commit hook (future work) to block accidental commits.

## Next Steps
- Add git-secrets or similar pre-commit scanning.
- Optional: integrate CI secret scanning (Gitleaks).
