const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkFollows() {
  try {
    // Check all Follow records
    const follows = await prisma.follow.findMany({
      include: {
        follower: { select: { id: true, name: true, email: true } },
        following: { select: { id: true, name: true, email: true } }
      }
    });
    
    console.log('Total Follow records:', follows.length);
    console.log('Follow records:', follows);
    
    // Check all users
    const users = await prisma.user.findMany({
      select: { id: true, name: true, email: true }
    });
    
    console.log('Total Users:', users.length);
    console.log('Users:', users);
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    await prisma.$disconnect();
  }
}

checkFollows();
