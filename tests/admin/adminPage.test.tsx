import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Static mocks
jest.mock('next/link', () => ({ __esModule: true, default: ({ children }: { children: React.ReactNode }) => <>{children}</> }));
jest.mock('next/image', () => ({ __esModule: true, default: (props: { alt?: string }) => <span data-testid="next-image" aria-label={props.alt || 'image'} /> }));
jest.mock('next-auth/react', () => ({ useSession: jest.fn() }));
import { useSession } from 'next-auth/react';

// Provide a consistent fetch mock
function mockFetch() {
  (global as unknown as { fetch: jest.Mock }).fetch = jest.fn(async (url: string) => {
    if (url === '/api/admin/stats') return { ok: true, json: async () => ({ users: 2, posts: 3, comments: 4 }) } as Response;
    if (url.startsWith('/api/admin/users')) return { ok: true, json: async () => ({ users: [] }) } as Response;
    if (url.startsWith('/api/communities')) return { ok: true, json: async () => ({ communities: [] }) } as Response;
    return { ok: true, json: async () => ({}) } as Response;
  });
}

import AdminPage from '@/app/admin/page';

describe('AdminDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch();
  });

  test('shows access denied for non-admin user', async () => {
  (useSession as unknown as jest.Mock).mockReturnValue({ data: { user: { id: 'u1', role: 'USER' } }, status: 'authenticated' });
    render(<AdminPage />);
    expect(await screen.findByText(/Access denied/i)).toBeInTheDocument();
  });

  test('renders analytics tab button for admin', async () => {
  (useSession as unknown as jest.Mock).mockReturnValue({ data: { user: { id: 'u1', role: 'ADMIN' } }, status: 'authenticated' });
    render(<AdminPage />);
    await waitFor(() => expect(screen.getByText(/Analytics/i)).toBeInTheDocument());
    // Ensure initial data fetch occurred
  const g: unknown = global;
  expect((g as { fetch: jest.Mock }).fetch).toHaveBeenCalledWith('/api/admin/stats');
  });
});
