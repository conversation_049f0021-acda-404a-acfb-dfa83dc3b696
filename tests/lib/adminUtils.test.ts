import { formatTimeAgo, getRoleBadgeColor } from '@/lib/adminUtils';

describe('adminUtils', () => {
  test('formatTimeAgo less than an hour', () => {
    const date = new Date(Date.now() - 10 * 60 * 1000);
    expect(formatTimeAgo(date)).toBe('Less than an hour ago');
  });
  test('formatTimeAgo hours', () => {
    const date = new Date(Date.now() - 5 * 60 * 60 * 1000);
    expect(formatTimeAgo(date)).toBe('5 hours ago');
  });
  test('formatTimeAgo days', () => {
    const date = new Date(Date.now() - 48 * 60 * 60 * 1000);
    expect(formatTimeAgo(date)).toBe('2 days ago');
  });
  test('getRoleBadgeColor mapping', () => {
    expect(getRoleBadgeColor('ADMIN')).toBe('badge-error');
    expect(getRoleBadgeColor('EDITOR')).toBe('badge-warning');
    expect(getRoleBadgeColor('USER')).toBe('badge-info');
  });
});
