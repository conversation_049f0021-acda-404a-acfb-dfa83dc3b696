import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
// Updated to use App Router not-found component (legacy pages/404 removed)
import NotFoundPage from '../../src/app/not-found';

// Mock Next.js Link component
jest.mock('next/link', () => {
  // eslint-disable-next-line react/display-name
  return ({ children, href, className }: { children: React.ReactNode; href: string; className?: string }) => {
    return <a href={href} className={className}>{children}</a>;
  };
});

describe('NotFoundPage', () => {
  test('test_renders_404_page_with_correct_content', () => {
    render(<NotFoundPage />);
    expect(screen.getByText('404')).toBeInTheDocument();
    expect(screen.getByText('Page Not Found')).toBeInTheDocument();
    expect(screen.getByText(/doesn't exist or was moved/i)).toBeInTheDocument();
  });

  test('test_displays_navigation_links_correctly', () => {
    render(<NotFoundPage />);
    
    const homeLink = screen.getByRole('link', { name: 'Go Home' });
    const searchLink = screen.getByRole('link', { name: 'Search' });
    
    expect(homeLink).toBeInTheDocument();
    expect(homeLink).toHaveAttribute('href', '/');
    expect(searchLink).toBeInTheDocument();
    expect(searchLink).toHaveAttribute('href', '/search');
  });

  test('test_applies_correct_css_classes_for_layout', () => {
    const { container } = render(<NotFoundPage />);
    const mainElement = container.querySelector('main');
    expect(mainElement).toBeInTheDocument();
    // New layout uses min-h-dvh and additional spacing classes
    expect(mainElement?.className).toEqual(expect.stringContaining('flex'));
    expect(mainElement?.className).toEqual(expect.stringContaining('items-center'));
    expect(mainElement?.className).toEqual(expect.stringContaining('justify-center'));
    const headingElement = screen.getByText('404');
    expect(headingElement.className).toEqual(expect.stringContaining('bg-gradient-to-r'));
  });

  test('test_handles_missing_props_gracefully', () => {
  render(<NotFoundPage />);
  // Should render without throwing and include the 404 marker once
  expect(screen.getByText('404')).toBeInTheDocument();
  });

  test('test_links_work_without_router_context', () => {
    render(<NotFoundPage />);
    
    const homeLink = screen.getByRole('link', { name: 'Go Home' });
    const searchLink = screen.getByRole('link', { name: 'Search' });
    
    expect(homeLink).toBeInTheDocument();
    expect(searchLink).toBeInTheDocument();
    expect(homeLink.getAttribute('href')).toBe('/');
    expect(searchLink.getAttribute('href')).toBe('/search');
  });

  test('test_basic_structure_present', () => {
    const { container } = render(<NotFoundPage />);
    expect(container.querySelector('main')).toBeInTheDocument();
    expect(screen.getByText('404')).toBeInTheDocument();
    expect(screen.getByText('Page Not Found')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Go Home' })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Search' })).toBeInTheDocument();
  });
});