import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

jest.mock('next/link', () => ({ __esModule: true, default: ({ children }: { children: React.ReactNode }) => <>{children}</> }));
jest.mock('next/image', () => ({ __esModule: true, default: (props: { alt?: string }) => <span data-testid="next-image" aria-label={props.alt || 'image'} /> }));
jest.mock('next-auth/react', () => ({ useSession: () => ({ data: null }) }));

const mockPosts = [{
  id: 'p1',
  title: 'First Post',
  content: 'Hello world content',
  published: true,
  images: [],
  videoUrl: null,
  createdAt: new Date(Date.now() - 3600_000).toISOString(),
  updatedAt: new Date().toISOString(),
  authorId: 'u1',
  author: { id: 'u1', name: '<PERSON>', email: '<EMAIL>', role: 'USER', image: '' },
  _count: { comments: 0, likes: 0 },
}];

(global as unknown as { fetch: jest.Mock }).fetch = jest.fn(async (url: string) => {
  if (url.startsWith('/api/posts?')) {
    return { ok: true, json: async () => ({ posts: mockPosts, pagination: { page: 1, limit: 10, total: 1, pages: 1 } }) } as Response;
  }
  if (url === '/api/posts/views') {
    return { ok: true, json: async () => ({ counts: { p1: 5 } }) } as Response;
  }
  throw new Error('Unexpected fetch ' + url);
});

import PostsPage from '@/app/posts/page';

describe('PostsPage', () => {
  it('renders fetched post title', async () => {
    render(<PostsPage />);
    await waitFor(() => expect(screen.getByText(/First Post/i)).toBeInTheDocument());
  });
});
