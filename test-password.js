// Test script to verify password hashing is working
const bcrypt = require('bcryptjs');

async function testPasswordHashing() {
  const testPassword = 'TestPassword123';
  
  console.log('Original password:', testPassword);
  
  // Hash the password
  const hashedPassword = await bcrypt.hash(testPassword, 12);
  console.log('Hashed password:', hashedPassword);
  console.log('Hash length:', hashedPassword.length);
  
  // Verify the password
  const isValid = await bcrypt.compare(testPassword, hashedPassword);
  console.log('Password verification:', isValid);
  
  // Test with wrong password
  const isInvalid = await bcrypt.compare('WrongPassword', hashedPassword);
  console.log('Wrong password verification:', isInvalid);
}

testPasswordHashing().catch(console.error);
