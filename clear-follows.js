const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function clearFollows() {
  try {
    console.log('Clearing all follow relationships...');
    
    const result = await prisma.follow.deleteMany({});
    
    console.log(`Deleted ${result.count} follow relationships`);
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    await prisma.$disconnect();
  }
}

clearFollows();
