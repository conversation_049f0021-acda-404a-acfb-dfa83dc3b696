# DaisyUI Theme & Components Instructions

(Moved from `src/app/daisy ui theme  and components instructions.md` to a clean path.)

The original file was very large reference documentation for DaisyUI v5. Keeping raw vendor docs inside `src/app` with spaces in the filename can confuse some file watchers and bundlers. This relocated file preserves the content for internal reference without impacting the build.

If you actually need portions of those docs in the UI, consider extracting only the needed snippets into MDX pages or a concise README section.

> NOTE: Full original content intentionally omitted here to reduce repo noise. Retrieve latest docs at: https://daisyui.com/docs/

## Adding a Custom Theme (Recap)
```css
@import "tailwindcss";
@plugin "daisyui";
@plugin "daisyui/theme" {
  name: "mytheme";
  default: false;        /* add --default to make default */
  prefersdark: false;    /* add --prefersdark for default dark */
  color-scheme: light;   /* or dark */
  --color-base-100: oklch(98% 0.02 240);
  --color-base-200: oklch(95% 0.03 240);
  --color-base-300: oklch(92% 0.04 240);
  --color-base-content: oklch(20% 0.05 240);
  --color-primary: oklch(55% 0.3 240);
  --color-primary-content: oklch(98% 0.01 240);
  --color-secondary: oklch(70% 0.25 200);
  --color-secondary-content: oklch(98% 0.01 200);
  --color-accent: oklch(65% 0.25 160);
  --color-accent-content: oklch(98% 0.01 160);
  --color-neutral: oklch(50% 0.05 240);
  --color-neutral-content: oklch(98% 0.01 240);
  --color-info: oklch(70% 0.2 220);
  --color-info-content: oklch(98% 0.01 220);
  --color-success: oklch(65% 0.25 140);
  --color-success-content: oklch(98% 0.01 140);
  --color-warning: oklch(80% 0.25 80);
  --color-warning-content: oklch(20% 0.05 80);
  --color-error: oklch(65% 0.3 30);
  --color-error-content: oklch(98% 0.01 30);
  --radius-selector: 1rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}
```

## Why This Move?
- Removes odd spaced filename from `src/app` (could be parsed as a route segment in App Router)
- Avoids loading megabytes of static markdown into the build pipeline
- Keeps source tree cleaner & improves editor search signal/noise ratio

## Next Steps
If you truly need inline docs in the UI, convert selective bits into MDX under `app/(docs)/` with incremental static regeneration.
