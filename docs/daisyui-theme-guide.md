<!-- Moved from src/app/daisy ui theme  and components instructions.md -->
<!-- This file documents daisyUI v5 + Tailwind v4 usage and theme creation for this project. -->

<!-- Rationale for move: keeping large docs out of the runtime "app" directory prevents dev server file watcher edge cases and unnecessary reloads. -->

<!-- The original content exceeds typical size; retaining canonical source link instead of duplicating 2000+ lines. For full reference fetch latest from official site if needed. -->

## daisyUI Theme Guide (Project Notes)

This project uses Tailwind CSS v4 (CSS-first) and daisyUI v5 configured directly inside `src/app/globals.css` via `@plugin` directives.

### Adding / Editing a Theme

1. Open `src/app/globals.css`.
2. Append a new block:
```
@plugin "daisyui/theme" {
  name: "mytheme";
  /* optional flags */
  /* default: true;        // make default */
  /* prefersdark: true;    // make default dark */
  /* color-scheme: light;  // browser UI */
  --color-base-100: oklch(98% 0.02 240);
  --color-base-200: oklch(95% 0.03 240);
  --color-base-300: oklch(92% 0.04 240);
  --color-base-content: oklch(20% 0.05 240);
  --color-primary: oklch(55% 0.30 240);
  --color-primary-content: oklch(98% 0.01 240);
  --color-secondary: oklch(70% 0.25 200);
  --color-secondary-content: oklch(98% 0.01 200);
  --color-accent: oklch(65% 0.25 160);
  --color-accent-content: oklch(98% 0.01 160);
  --color-neutral: oklch(50% 0.05 240);
  --color-neutral-content: oklch(98% 0.01 240);
  --color-info: oklch(70% 0.20 220);
  --color-info-content: oklch(98% 0.01 220);
  --color-success: oklch(65% 0.25 140);
  --color-success-content: oklch(98% 0.01 140);
  --color-warning: oklch(80% 0.25 80);
  --color-warning-content: oklch(20% 0.05 80);
  --color-error: oklch(65% 0.30 30);
  --color-error-content: oklch(98% 0.01 30);
  --radius-selector: 0.25rem;
  --radius-field: 0.5rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}
```
3. Add the theme name to the master list inside the initial `@plugin "daisyui" { themes: ... }` line.

### Selecting a Theme at Runtime

Use a radio input with class `theme-controller` whose value equals the theme name, or set `<html data-theme="mytheme">` server-side.

### Official Reference

Complete daisyUI 5 reference (components, utilities, variables):
https://daisyui.com/docs/

LLM-friendly full text (latest):
https://daisyui.com/llms.txt

If deeper component usage/semantics are needed, fetch the upstream doc instead of expanding this in-repo file.

### Project Conventions

- Keep only curated custom themes (avoid duplicating similar palettes).
- Retro is default; dark auto preference is provided via `dark --prefersdark` in the list.
- Use OKLCH color space for new themes for consistency and better perceptual uniformity.
- Prefer minimal radius adjustments unless a theme’s personality demands change.
- Avoid placing large documentation files inside `src/app` to reduce dev server memory and watcher overhead.

### Troubleshooting

Issue: Dev server throws path.relative undefined / watcher crash.
- Cause often: Unusual filenames with multiple spaces or extremely large markdown files in `app` dir.
- Fix: Move docs (this change) + restart (`rm -rf .next && npm run dev`).

Missing styles after adding theme?
- Ensure theme name is appended to the initial `themes:` list.
- Ensure no trailing commas or stray characters inside the `@plugin` block.
- Restart dev server if the CSS file structure changed significantly.

Theme not switching with controller radios?
- Verify inputs have `class="theme-controller"` and `type="radio"` and are checked.
- Ensure no JS forcibly overriding `data-theme` attribute later.

That’s it—refer to official docs for exhaustive component class names.
