#!/usr/bin/env ts-node
/**
 * Script: cleanup-self-follows.ts
 * Removes any accidental self-follow rows (followerId == followingId).
 */
import { prisma } from '../src/lib/prisma';

async function run() {
  const targetUserId = process.argv[2];
  const whereClause: any = targetUserId ? { followerId: targetUserId, followingId: targetUserId } : { followerId: { equals: prisma.$queryRaw`followerId` } };
  // Simpler: just delete rows where followerId == followingId using raw SQL (portable for Postgres)
  const condition = targetUserId ? `WHERE "followerId"='${targetUserId}' AND "followingId"='${targetUserId}'` : 'WHERE "followerId" = "followingId"';
  const result: any = await prisma.$executeRawUnsafe(`DELETE FROM "Follow" ${condition};`);
  console.log(`Self-follow cleanup complete. Rows deleted: ${result}`);
}

run().then(()=>process.exit(0)).catch(err=>{console.error(err);process.exit(1);});
