#!/bin/bash

# Setup script for Forum Application with Docker
set -e

echo "🚀 Setting up Forum Application with Docker..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_success "Docker and Docker Compose are installed"

# Check if .env.local exists, if not create from template
if [ ! -f .env.local ]; then
    print_status "Creating .env.local from template..."
    cp .env.docker .env.local
    print_warning "Please edit .env.local with your configuration before running the application"
else
    print_status ".env.local already exists"
fi

# Make scripts executable
print_status "Making scripts executable..."
chmod +x docker-entrypoint.sh
chmod +x scripts/health-check.sh
chmod +x scripts/setup.sh

# Build and start services
print_status "Building and starting Docker services..."
docker-compose up --build -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 30

# Run health check
print_status "Running health checks..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    print_success "Application is running and healthy!"
else
    print_warning "Application might still be starting up. Please wait a moment and try again."
fi

# Display useful information
echo ""
print_success "🎉 Setup completed!"
echo ""
echo "📋 Quick Reference:"
echo "  Application URL: http://localhost:3000"
echo "  Database: PostgreSQL on port 5432"
echo "  Redis: Redis on port 6379"
echo ""
echo "🔧 Useful Commands:"
echo "  View logs: docker-compose logs -f"
echo "  Stop services: docker-compose down"
echo "  Restart services: docker-compose restart"
echo "  Health check: curl http://localhost:3000/api/health"
echo ""
echo "📚 Demo Accounts:"
echo "  Admin: <EMAIL>"
echo "  Editor: <EMAIL>"
echo "  User: <EMAIL>"
echo "  Password: password123 (for all accounts)"
echo ""
print_status "Check DOCKER.md for detailed documentation"
