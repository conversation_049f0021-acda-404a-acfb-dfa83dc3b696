#!/usr/bin/env bash
set -euo pipefail

# Backup PostgreSQL database defined by DATABASE_URL
# Creates file backups/pg-backup-YYYYMMDD_HHMMSS.sql.gz

if [[ -z "${DATABASE_URL:-}" ]]; then
  echo "DATABASE_URL is not set. Load your env first (e.g., source .env)" >&2
  exit 1
fi

mkdir -p backups
STAMP=$(date +%Y%m%d_%H%M%S)
OUT="backups/pg-backup-${STAMP}.sql.gz"

# Parse DATABASE_URL (postgres://user:pass@host:port/dbname)
proto=$(echo "$DATABASE_URL" | sed -E 's#(.*)://.*#\1#')
if [[ "$proto" != "postgres" && "$proto" != "postgresql" ]]; then
  echo "Unsupported DATABASE_URL protocol: $proto" >&2
  exit 1
fi

# Use pg_dump directly with the URL
if ! command -v pg_dump >/dev/null 2>&1; then
  echo "pg_dump not found. Please install PostgreSQL client tools." >&2
  exit 1
fi

# PGPASSWORD is handled inside URL so we can pass the URL to pg_dump
pg_dump --no-owner --no-acl --format=plain --verbose "$DATABASE_URL" | gzip -9 > "$OUT"

if [[ -s "$OUT" ]]; then
  echo "Backup written to: $OUT"
else
  echo "Backup failed: $OUT is empty" >&2
  exit 1
fi
