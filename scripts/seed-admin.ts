#!/usr/bin/env ts-node
import 'dotenv/config';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

async function main() {
  const email = '<EMAIL>';
  const name = '<PERSON><PERSON><PERSON>';
  const plainPassword = 'b1U8S1cS4cFlCm';

  const existing = await prisma.user.findUnique({ where: { email } });
  if (existing) {
    if (existing.role !== 'ADMIN') {
      await prisma.user.update({ where: { email }, data: { role: 'ADMIN' } });
      console.log(`Updated existing user ${email} to ADMIN role.`);
    } else {
      console.log(`Admin user already exists: ${email}`);
    }
    return;
  }

  const password = await bcrypt.hash(plainPassword, 12);
  const user = await prisma.user.create({
    data: {
      email,
      name,
      role: 'ADMIN',
      password,
    }
  });
  console.log('Created admin user:', { id: user.id, email: user.email, role: user.role });
}

main().catch(e => {
  console.error(e);
  process.exit(1);
}).finally(async () => {
  await prisma.$disconnect();
});
