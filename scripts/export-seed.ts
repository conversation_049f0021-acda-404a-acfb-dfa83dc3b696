#!/usr/bin/env tsx
import 'dotenv/config';
import { promises as fs } from 'fs';
import path from 'path';
import { prisma } from '@/lib/prisma';

async function ensureDir(p: string) {
  await fs.mkdir(p, { recursive: true });
}

function ts() {
  const d = new Date();
  const pad = (n: number) => n.toString().padStart(2, '0');
  return (
    d.getFullYear().toString() +
    pad(d.getMonth() + 1) +
    pad(d.getDate()) + '_' +
    pad(d.getHours()) +
    pad(d.getMinutes()) +
    pad(d.getSeconds())
  );
}

async function main() {
  console.log('[seed-export] Starting export from database...');

  // Fetch all tables. Keep it simple and separate per model
  const [
    users,
    accounts,
    sessions,
    forumSettings,
    verificationTokens,
    communities,
    communityMembers,
    posts,
    comments,
    likes,
    follows,
    reports,
    notifications,
  ] = await Promise.all([
    prisma.user.findMany(),
    prisma.account.findMany(),
    prisma.session.findMany(),
    prisma.forumSettings.findMany(),
    prisma.verificationToken.findMany(),
    prisma.community.findMany(),
    prisma.communityMember.findMany(),
    prisma.post.findMany(),
    prisma.comment.findMany(),
    prisma.like.findMany(),
    prisma.follow.findMany(),
    prisma.report.findMany(),
    prisma.notification.findMany(),
  ]);

  const payload = {
    meta: {
      exportedAt: new Date().toISOString(),
      models: {
        users: users.length,
        accounts: accounts.length,
        sessions: sessions.length,
        forumSettings: forumSettings.length,
        verificationTokens: verificationTokens.length,
        communities: communities.length,
        communityMembers: communityMembers.length,
        posts: posts.length,
        comments: comments.length,
        likes: likes.length,
        follows: follows.length,
        reports: reports.length,
        notifications: notifications.length,
      },
    },
    data: {
      users,
      accounts,
      sessions,
      forumSettings,
      verificationTokens,
      communities,
      communityMembers,
      posts,
      comments,
      likes,
      follows,
      reports,
      notifications,
    },
  };

  const backupsDir = path.resolve('backups');
  await ensureDir(backupsDir);

  const filename = `seed-${ts()}.json`;
  const fullPath = path.join(backupsDir, filename);
  await fs.writeFile(fullPath, JSON.stringify(payload, null, 2), 'utf8');
  console.log(`[seed-export] Wrote ${fullPath}`);

  // Also update a canonical prisma/seed-data.json for default seeding
  const prismaSeedDataPath = path.resolve('prisma', 'seed-data.json');
  await ensureDir(path.dirname(prismaSeedDataPath));
  await fs.writeFile(prismaSeedDataPath, JSON.stringify(payload, null, 2), 'utf8');
  console.log(`[seed-export] Updated ${prismaSeedDataPath}`);

  console.log('[seed-export] Done.');
}

main()
  .catch((e) => {
    console.error('[seed-export] Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
