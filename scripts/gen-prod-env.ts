#!/usr/bin/env ts-node
/**
 * Generates a production .env.prod file with strong random secrets.
 * Does NOT overwrite an existing .env.prod unless --force passed.
 */
import { randomBytes } from 'crypto';
import { existsSync, writeFileSync, readFileSync } from 'fs';
import path from 'path';

function hex(bytes: number) { return randomBytes(bytes).toString('hex'); }
function base64(bytes: number) { return randomBytes(bytes).toString('base64'); }

const force = process.argv.includes('--force');
const outFile = path.resolve(process.cwd(), '.env.prod');

if (existsSync(outFile) && !force) {
  console.error('.env.prod already exists. Use --force to overwrite.');
  process.exit(1);
}

// Domain placeholder – user should edit after generation
const domain = 'https://com4.myprox.eu';

// Generate secrets
const nextAuthSecret = hex(64); // 128 hex chars
const dbPassword = `pg_${hex(16)}`;
const redisPassword = `redis_${hex(16)}`;
const csrfSecret = hex(48); // 96 hex chars

// Compose
const content = `# AUTO-GENERATED production environment file\n# Edit NEXTAUTH_URL if domain differs. Generated at ${new Date().toISOString()}\n\nNODE_ENV=production\nAPP_PORT=3131\nNEXTAUTH_URL=${domain}\nNEXTAUTH_SECRET=${nextAuthSecret}\n\nPOSTGRES_DB=forum_db\nPOSTGRES_USER=forum_user\nPOSTGRES_PASSWORD=${dbPassword}\nPOSTGRES_PORT=5432\nDATABASE_URL=postgresql://forum_user:${dbPassword}@postgres:5432/forum_db?schema=public\n\nREDIS_HOST=redis\nREDIS_PORT=6379\nREDIS_PASSWORD=${redisPassword}\nREDIS_TLS_ENABLED=false\nREDIS_CACHE_TTL=3600\n\nMAX_FILE_SIZE=10485760\nALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp\nUPLOAD_STRATEGY=local\n\nSECURE_COOKIES=true\nCSRF_SECRET=${csrfSecret}\nSESSION_COOKIE_NAME=__Host_forum_session\nTRUST_PROXY=true\n\nLOG_LEVEL=info\nENABLE_REQUEST_LOGGING=false\n\nFEATURE_ENABLE_FOLLOWS=true\nFEATURE_ENABLE_IMAGE_UPLOADS=true\nFEATURE_ENABLE_NOTIFICATIONS=true\n\nWATCHPACK_POLLING=false\nTURBOPACK=false\n`;

writeFileSync(outFile, content, { encoding: 'utf8', flag: 'w' });
console.log('Generated .env.prod with random secrets.');
