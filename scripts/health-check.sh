#!/bin/bash

# Health check script for the forum application
set -e

echo "🏥 Running health checks..."

# Check if the application is responding
echo "📡 Checking application health..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ Application is healthy"
else
    echo "❌ Application health check failed"
    exit 1
fi

# Check database connection
echo "🗄️ Checking database connection..."
if docker-compose exec -T postgres pg_isready -U forum_user -d forum_db > /dev/null 2>&1; then
    echo "✅ Database is healthy"
else
    echo "❌ Database health check failed"
    exit 1
fi

# Check Redis connection (if available)
echo "🔴 Checking Redis connection..."
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is healthy"
else
    echo "⚠️ Redis health check failed (optional service)"
fi

echo "🎉 All health checks passed!"
