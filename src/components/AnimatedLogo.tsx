"use client";

import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';

interface AnimatedLogoProps {
  src: string;
  alt: string;
  className?: string;
  size?: 'small' | 'medium' | 'large' | 'xl';
  animationType?: 'float' | 'pulse' | 'rotate' | 'bounce' | 'glow';
  scrollAnimation?: boolean;
}

export default function AnimatedLogo({ 
  src, 
  alt, 
  className = '', 
  size = 'large',
  animationType = 'float',
  scrollAnimation = false
}: AnimatedLogoProps) {
  const logoRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isScrolled, setIsScrolled] = useState(false);

  const getSizeClasses = () => {
    const baseSize = (() => {
      switch (size) {
        case 'small':
          return 'w-32 h-32 md:w-40 md:h-40';
        case 'medium':
          return 'w-48 h-48 md:w-56 md:h-56';
        case 'large':
          return 'w-64 h-64 md:w-80 md:h-80';
        case 'xl':
          return 'w-80 h-80 md:w-96 md:h-96 lg:w-[28rem] lg:h-[28rem]';
        default:
          return 'w-64 h-64 md:w-80 md:h-80';
      }
    })();

    // If scroll animation is enabled, apply size reduction when scrolled
    if (scrollAnimation && isScrolled) {
      switch (size) {
        case 'small':
          return 'w-20 h-20 md:w-24 md:h-24';
        case 'medium':
          return 'w-32 h-32 md:w-40 md:h-40';
        case 'large':
          return 'w-48 h-48 md:w-56 md:h-56';
        case 'xl':
          return 'w-56 h-56 md:w-64 md:h-64 lg:w-80 lg:h-80';
        default:
          return 'w-48 h-48 md:w-56 md:h-56';
      }
    }

    return baseSize;
  };

  useEffect(() => {
    if (!scrollAnimation) return;

    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 200); // Trigger animation after scrolling 200px
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [scrollAnimation]);

  // No GSAP: rely on CSS animations via utility classes decided by animationType
  const animationClass = (() => {
    switch (animationType) {
      case 'float':
        return 'animate-float';
      case 'pulse':
        return 'animate-pulse-glow';
      case 'rotate':
        return 'animate-spin-slow';
      case 'bounce':
        return 'animate-bounce';
      case 'glow':
        return 'animate-pulse-glow';
      default:
        return 'animate-float';
    }
  })();

  return (
    <div 
      ref={containerRef}
      className={`flex items-center justify-center cursor-pointer ${className}`}
    >
      <div ref={logoRef} className={`relative ${getSizeClasses()}`}>
        <Image
          src={src}
          alt={alt}
          fill
          className={`object-contain select-none transition-all duration-500 ${animationClass} ${
            scrollAnimation && isScrolled ? 'opacity-80 scale-95' : 'opacity-100 scale-100'
          } drop-shadow-xl`}
          draggable={false}
          priority={size === 'xl'}
        />
      </div>
    </div>
  );
}
