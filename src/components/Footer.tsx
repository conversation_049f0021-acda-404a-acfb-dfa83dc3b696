"use client";

import Link from "next/link";
import Image from "next/image";
import { Icons, iconSizes } from "./icons";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-base-200/50 backdrop-blur-sm border-t border-primary/10 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-4">
              <Image 
                src="/favicon.png" 
                alt="COMFOR" 
                width={40} 
                height={40} 
                className="w-10 h-10 object-contain"
              />
              <div>
                <h3 className="text-xl font-bold text-primary">COMFOR</h3>
                <p className="text-sm text-base-content/70">Communities Forum</p>
              </div>
            </div>
            <p className="text-base-content/80 text-sm leading-relaxed mb-4">
              A modern communities forum platform with role-based access control and real-time discussions. 
              Connect, share, and engage with communities that matter to you.
            </p>
            <div className="flex space-x-3">
              <button className="btn btn-ghost btn-sm rounded-full hover:bg-primary/10 transition-all duration-300">
                <Icons.Heart className={iconSizes.sm} />
              </button>
              <button className="btn btn-ghost btn-sm rounded-full hover:bg-primary/10 transition-all duration-300">
                <Icons.Share className={iconSizes.sm} />
              </button>
              <button className="btn btn-ghost btn-sm rounded-full hover:bg-primary/10 transition-all duration-300">
                <Icons.Star className={iconSizes.sm} />
              </button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-base-content mb-4 flex items-center">
              <Icons.Home className={`${iconSizes.sm} mr-2 text-primary`} />
              Quick Links
            </h4>
            <ul className="space-y-2">
              <li>
                <Link 
                  href="/" 
                  className="text-base-content/70 hover:text-primary transition-colors duration-200 text-sm flex items-center group"
                >
                  <Icons.ChevronRight className={`${iconSizes.xs} mr-1 group-hover:translate-x-1 transition-transform duration-200`} />
                  Home
                </Link>
              </li>
              <li>
                <Link 
                  href="/communities" 
                  className="text-base-content/70 hover:text-primary transition-colors duration-200 text-sm flex items-center group"
                >
                  <Icons.ChevronRight className={`${iconSizes.xs} mr-1 group-hover:translate-x-1 transition-transform duration-200`} />
                  Communities
                </Link>
              </li>
              <li>
                <Link 
                  href="/posts/create" 
                  className="text-base-content/70 hover:text-primary transition-colors duration-200 text-sm flex items-center group"
                >
                  <Icons.ChevronRight className={`${iconSizes.xs} mr-1 group-hover:translate-x-1 transition-transform duration-200`} />
                  Create Post
                </Link>
              </li>
              <li>
                <Link 
                  href="/profile" 
                  className="text-base-content/70 hover:text-primary transition-colors duration-200 text-sm flex items-center group"
                >
                  <Icons.ChevronRight className={`${iconSizes.xs} mr-1 group-hover:translate-x-1 transition-transform duration-200`} />
                  Profile
                </Link>
              </li>
            </ul>
          </div>

          {/* Community Features */}
          <div>
            <h4 className="font-semibold text-base-content mb-4 flex items-center">
              <Icons.Users className={`${iconSizes.sm} mr-2 text-primary`} />
              Features
            </h4>
            <ul className="space-y-2">
              <li className="text-base-content/70 text-sm flex items-center">
                <Icons.Check className={`${iconSizes.xs} mr-2 text-success`} />
                Real-time Discussions
              </li>
              <li className="text-base-content/70 text-sm flex items-center">
                <Icons.Check className={`${iconSizes.xs} mr-2 text-success`} />
                Role-based Access
              </li>
              <li className="text-base-content/70 text-sm flex items-center">
                <Icons.Check className={`${iconSizes.xs} mr-2 text-success`} />
                Media Sharing
              </li>
              <li className="text-base-content/70 text-sm flex items-center">
                <Icons.Check className={`${iconSizes.xs} mr-2 text-success`} />
                Community Management
              </li>
              <li className="text-base-content/70 text-sm flex items-center">
                <Icons.Check className={`${iconSizes.xs} mr-2 text-success`} />
                Admin Dashboard
              </li>
            </ul>
          </div>

          {/* Support & Info */}
          <div>
            <h4 className="font-semibold text-base-content mb-4 flex items-center">
              <Icons.Info className={`${iconSizes.sm} mr-2 text-primary`} />
              Support
            </h4>
            <ul className="space-y-2">
              <li>
                <button className="text-base-content/70 hover:text-primary transition-colors duration-200 text-sm flex items-center group">
                  <Icons.ChevronRight className={`${iconSizes.xs} mr-1 group-hover:translate-x-1 transition-transform duration-200`} />
                  Help Center
                </button>
              </li>
              <li>
                <button className="text-base-content/70 hover:text-primary transition-colors duration-200 text-sm flex items-center group">
                  <Icons.ChevronRight className={`${iconSizes.xs} mr-1 group-hover:translate-x-1 transition-transform duration-200`} />
                  Community Guidelines
                </button>
              </li>
              <li>
                <button className="text-base-content/70 hover:text-primary transition-colors duration-200 text-sm flex items-center group">
                  <Icons.ChevronRight className={`${iconSizes.xs} mr-1 group-hover:translate-x-1 transition-transform duration-200`} />
                  Privacy Policy
                </button>
              </li>
              <li>
                <button className="text-base-content/70 hover:text-primary transition-colors duration-200 text-sm flex items-center group">
                  <Icons.ChevronRight className={`${iconSizes.xs} mr-1 group-hover:translate-x-1 transition-transform duration-200`} />
                  Terms of Service
                </button>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="py-6 border-t border-primary/10">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <p className="text-base-content/60 text-sm">
                © {currentYear} COMFOR. All rights reserved.
              </p>
              <div className="hidden sm:flex items-center space-x-2">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                <span className="text-success text-xs font-medium">Online</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-base-content/60 text-xs">Made with</span>
              <Icons.Heart className={`${iconSizes.xs} text-error animate-pulse`} />
              <span className="text-base-content/60 text-xs">for communities</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
