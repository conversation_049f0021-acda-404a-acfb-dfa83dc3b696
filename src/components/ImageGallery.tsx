"use client";

import { useState } from "react";

interface ImageGalleryProps {
  images: string[];
  className?: string;
  maxPreview?: number;
  showLightbox?: boolean;
}

export default function ImageGallery({
  images,
  className = "",
  maxPreview = 4,
  showLightbox = true
}: ImageGalleryProps) {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  if (!images || images.length === 0) {
    return null;
  }

  const openLightbox = (index: number) => {
    if (showLightbox) {
      setCurrentImageIndex(index);
      setLightboxOpen(true);
    }
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const getGridLayout = () => {
    const visibleImages = images.slice(0, maxPreview);
    const remainingCount = images.length - maxPreview;

    if (visibleImages.length === 1) {
      return "grid-cols-1";
    } else if (visibleImages.length === 2) {
      return "grid-cols-2";
    } else if (visibleImages.length === 3) {
      return "grid-cols-2";
    } else {
      return "grid-cols-2";
    }
  };

  const getImageClasses = (index: number, total: number) => {
    if (total === 1) {
      return "col-span-1 aspect-video";
    } else if (total === 2) {
      return "col-span-1 aspect-square";
    } else if (total === 3) {
      if (index === 0) return "col-span-2 aspect-video";
      return "col-span-1 aspect-square";
    } else {
      return "col-span-1 aspect-square";
    }
  };

  const visibleImages = images.slice(0, maxPreview);
  const remainingCount = images.length - maxPreview;

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Image Grid */}
      <div className={`grid gap-2 ${getGridLayout()}`}>
        {visibleImages.map((image, index) => (
          <div
            key={index}
            className={`relative overflow-hidden rounded-xl cursor-pointer group ${getImageClasses(index, visibleImages.length)}`}
            onClick={() => openLightbox(index)}
          >
            <img
              src={image}
              alt={`Image ${index + 1}`}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            
            {/* Hover Overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>

            {/* Remaining Count Overlay */}
            {index === maxPreview - 1 && remainingCount > 0 && (
              <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="text-2xl font-bold">+{remainingCount}</div>
                  <div className="text-sm">more images</div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Image Count */}
      {images.length > 1 && (
        <div className="text-xs text-base-content/60 flex items-center space-x-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <span>{images.length} image{images.length !== 1 ? 's' : ''}</span>
        </div>
      )}

      {/* Lightbox Modal */}
      {showLightbox && lightboxOpen && (
        <div className="modal modal-open">
          <div className="modal-box max-w-6xl max-h-screen p-0 bg-black">
            {/* Header */}
            <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black to-transparent p-4">
              <div className="flex items-center justify-between text-white">
                <div className="text-sm">
                  {currentImageIndex + 1} of {images.length}
                </div>
                <button
                  onClick={closeLightbox}
                  className="btn btn-ghost btn-sm btn-circle text-white hover:bg-white/20"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Image */}
            <div className="relative flex items-center justify-center min-h-screen p-4">
              <img
                src={images[currentImageIndex]}
                alt={`Image ${currentImageIndex + 1}`}
                className="max-w-full max-h-full object-contain"
              />

              {/* Navigation */}
              {images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 -translate-y-1/2 btn btn-ghost btn-circle text-white hover:bg-white/20"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 -translate-y-1/2 btn btn-ghost btn-circle text-white hover:bg-white/20"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </>
              )}
            </div>

            {/* Thumbnails */}
            {images.length > 1 && (
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-4">
                <div className="flex justify-center space-x-2 overflow-x-auto">
                  {images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                        index === currentImageIndex
                          ? 'border-primary scale-110'
                          : 'border-white/30 hover:border-white/60'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
