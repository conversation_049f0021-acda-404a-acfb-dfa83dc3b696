"use client";

import Link from "next/link";
import { useState } from "react";
import FollowButton from "./FollowButton";

interface User {
  id: string;
  name: string;
  username: string;
  email: string;
  avatar: string;
  bio: string;
  location?: string;
  postCount: number;
  followerCount: number;
  followingCount: number;
  isFollowing: boolean;
  isOnline: boolean;
  badges?: string[];
  communities?: string[];
  joinedDate?: Date;
}

interface UserCardProps {
  user: User;
  onFollowChange?: (userId: string, isFollowing: boolean, newCount: number) => void;
  variant?: "default" | "compact" | "detailed";
  showFollowButton?: boolean;
  className?: string;
}

export default function UserCard({
  user,
  onFollowChange,
  variant = "default",
  showFollowButton = true,
  className = ""
}: UserCardProps) {
  const [localUser, setLocalUser] = useState(user);

  const handleFollowChange = (userId: string, isFollowing: boolean, newCount: number) => {
    setLocalUser(prev => ({
      ...prev,
      isFollowing,
      followerCount: newCount
    }));
    onFollowChange?.(userId, isFollowing, newCount);
  };

  const formatJoinDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short' 
    });
  };

  if (variant === "compact") {
    return (
      <div className={`card bg-gradient-to-br from-base-100 to-base-200/50 shadow-lg border border-primary/10 hover:shadow-xl transition-all duration-300 ${className}`}>
        <div className="card-body p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="avatar">
                <div className="w-12 h-12 rounded-full ring-2 ring-primary/20 relative">
                  <img src={localUser.avatar} alt={localUser.name} />
                  {localUser.isOnline && (
                    <div className="absolute bottom-0 right-0 w-3 h-3 bg-success rounded-full border-2 border-base-100"></div>
                  )}
                </div>
              </div>
              <div>
                <Link href={`/users/${localUser.username}`} className="font-semibold hover:text-primary transition-colors">
                  {localUser.name}
                </Link>
                <p className="text-xs text-base-content/60">@{localUser.username}</p>
              </div>
            </div>
            {showFollowButton && (
              <FollowButton
                userId={localUser.id}
                username={localUser.username}
                isFollowing={localUser.isFollowing}
                followerCount={localUser.followerCount}
                onFollowChange={handleFollowChange}
                size="sm"
                variant="outline"
              />
            )}
          </div>
        </div>
      </div>
    );
  }

  if (variant === "detailed") {
    return (
      <div className={`card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 ${className}`}>
        <div className="card-body p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-4">
              <div className="avatar">
                <div className="w-16 h-16 rounded-full ring-2 ring-primary/20 relative">
                  <img src={localUser.avatar} alt={localUser.name} />
                  {localUser.isOnline && (
                    <div className="absolute bottom-0 right-0 w-4 h-4 bg-success rounded-full border-2 border-base-100"></div>
                  )}
                </div>
              </div>
              <div>
                <Link href={`/profile/${localUser.id}`} className="text-xl font-bold hover:text-primary transition-colors">
                  {localUser.name}
                </Link>
                <p className="text-base-content/60">@{localUser.username}</p>
                {localUser.location && (
                  <p className="text-base-content/50 text-sm flex items-center mt-1">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {localUser.location}
                  </p>
                )}
              </div>
            </div>
            {localUser.isFollowing && (
              <div className="badge badge-success badge-sm">Following</div>
            )}
          </div>

          {/* Bio */}
          <p className="text-base-content/70 text-sm mb-4 line-clamp-3">
            {localUser.bio}
          </p>

          {/* Badges */}
          {localUser.badges && localUser.badges.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-4">
              {localUser.badges.map((badge, index) => (
                <span key={index} className="badge badge-outline badge-xs">
                  {badge}
                </span>
              ))}
            </div>
          )}

          {/* Stats */}
          <div className="grid grid-cols-3 gap-2 mb-4">
            <div className="text-center">
              <div className="text-lg font-bold text-primary">{localUser.postCount || 0}</div>
              <div className="text-xs text-base-content/60">Posts</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-accent">{localUser.followerCount || 0}</div>
              <div className="text-xs text-base-content/60">Followers</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-info">{localUser.followingCount || 0}</div>
              <div className="text-xs text-base-content/60">Following</div>
            </div>
          </div>

          {/* Join Date */}
          {localUser.joinedDate && (
            <p className="text-xs text-base-content/50 mb-4">
              Joined {formatJoinDate(localUser.joinedDate)}
            </p>
          )}

          {/* Actions */}
          <div className="card-actions justify-between">
            <Link
              href={`/profile/${localUser.id}`}
              className="btn btn-ghost btn-sm rounded-full"
            >
              View Profile
            </Link>
            {showFollowButton && (
              <FollowButton
                userId={localUser.id}
                username={localUser.username}
                isFollowing={localUser.isFollowing}
                followerCount={localUser.followerCount}
                onFollowChange={handleFollowChange}
                size="sm"
              />
            )}
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10 hover:shadow-2xl transition-all duration-300 ${className}`}>
      <div className="card-body p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="avatar">
            <div className="w-14 h-14 rounded-full ring-2 ring-primary/20 relative">
              <img src={localUser.avatar} alt={localUser.name} />
              {localUser.isOnline && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-success rounded-full border-2 border-base-100"></div>
              )}
            </div>
          </div>
          <div className="flex-1">
            <Link href={`/profile/${localUser.id}`} className="font-bold text-lg hover:text-primary transition-colors">
              {localUser.name}
            </Link>
            <p className="text-base-content/60 text-sm">@{localUser.username}</p>
          </div>
          {showFollowButton && (
            <FollowButton
              userId={localUser.id}
              username={localUser.username}
              isFollowing={localUser.isFollowing}
              followerCount={localUser.followerCount}
              onFollowChange={handleFollowChange}
              size="sm"
              variant="outline"
            />
          )}
        </div>
        
        <p className="text-base-content/70 text-sm mb-4">{localUser.bio}</p>
        
        <div className="flex justify-between text-sm text-base-content/60">
          <span>{localUser.postCount || 0} posts</span>
          <span>{localUser.followerCount || 0} followers</span>
        </div>
      </div>
    </div>
  );
}
