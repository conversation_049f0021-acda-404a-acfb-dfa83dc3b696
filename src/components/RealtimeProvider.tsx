"use client";

import { useEffect, useRef, ReactNode, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useSession } from 'next-auth/react';

interface RealtimeProviderProps {
  children: ReactNode;
}

export function RealtimeProvider({ children }: RealtimeProviderProps) {
  const { data: session, status } = useSession();
  const socketRef = useRef<Socket | null>(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    // Only initialize socket if session is loaded (not loading)
    if (status === 'loading') return;
    
    // Avoid multiple connections
    if (!socketRef.current) {
      const endpoint = process.env.NEXT_PUBLIC_REALTIME_URL || 'http://localhost:4001';
      const socket = io(endpoint, {
        // Remove custom path; default client path /socket.io won't hit Next route handler anyway
        transports: ['websocket', 'polling'],
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });
      socketRef.current = socket;

      socket.on('connect', () => setConnected(true));
      socket.on('disconnect', () => setConnected(false));
      socket.on('connect_error', (err) => {
        console.warn('[Realtime] connect_error', err.message);
      });

      // Example listeners for demonstration
      socket.on('post:new', (data) => {
        // Could integrate toast system here
        console.debug('[Realtime] New post broadcast', data?.id);
      });

      socket.on('notification', (data) => {
        console.debug('[Realtime] Notification', data?.type);
      });
    }

    // Join user-specific room once session available
    interface SessionUser {
      id?: string;
      [key: string]: unknown;
    }
    const userId = (session?.user as SessionUser)?.id;
    if (userId && socketRef.current) {
      socketRef.current.emit('join', `user:${userId}`);
    }

    return () => {
      // Keep singleton across fast refresh; only disconnect on full unmount
      if (socketRef.current) {
        socketRef.current.off('connect');
        socketRef.current.off('disconnect');
        socketRef.current.off('connect_error');
      }
    };
  }, [session, status]);

  return (
    <>
      {/* Connection indicator (visually hidden for now) */}
      <span data-socket-status={connected ? 'online' : 'offline'} style={{ display: 'none' }} />
      {children}
    </>
  );
}
