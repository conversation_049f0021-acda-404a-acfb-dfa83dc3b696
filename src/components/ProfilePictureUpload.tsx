"use client";

import { useState } from "react";
import ImageUpload from "./ImageUpload";

interface ProfilePictureUploadProps {
  currentImage?: string;
  onImageUpdate: (imageUrl: string) => void;
  size?: "sm" | "md" | "lg" | "xl";
  showEditButton?: boolean;
  className?: string;
}

export default function ProfilePictureUpload({
  currentImage,
  onImageUpdate,
  size = "lg",
  showEditButton = true,
  className = ""
}: ProfilePictureUploadProps) {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "w-16 h-16";
      case "md":
        return "w-24 h-24";
      case "lg":
        return "w-32 h-32";
      case "xl":
        return "w-48 h-48";
      default:
        return "w-32 h-32";
    }
  };

  const handleImageSelect = async (file: File, preview: string) => {
    setIsUploading(true);
    try {
      // Upload the file
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        onImageUpdate(data.url);
        setShowUploadModal(false);
      } else {
        alert('Failed to upload image');
      }
    } catch (error) {
      console.error('Failed to upload avatar:', error);
      alert('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Avatar Display */}
      <div className={`avatar ${showEditButton ? 'cursor-pointer group' : ''}`}>
        <div 
          className={`${getSizeClasses()} rounded-full ring-4 ring-primary/20 hover:ring-primary/40 transition-all duration-300 relative overflow-hidden`}
          onClick={showEditButton ? () => setShowUploadModal(true) : undefined}
        >
          {currentImage ? (
            <img
              src={currentImage}
              alt="Profile"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
              <svg className="w-1/2 h-1/2 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
          )}
          
          {/* Edit Overlay */}
          {showEditButton && (
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
          )}
        </div>
      </div>

      {/* Edit Button */}
      {showEditButton && (
        <button
          onClick={() => setShowUploadModal(true)}
          className="absolute -bottom-2 -right-2 btn btn-primary btn-sm btn-circle shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </button>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="modal modal-open z-[70]">
          <div className="modal-box">
            <h3 className="font-bold text-lg mb-4">Update Profile Picture</h3>
            
            <div className="space-y-6">
              <ImageUpload
                onImageSelect={handleImageSelect}
                currentImage={currentImage}
                maxSize={5}
                aspectRatio={1}
                shape="circle"
                placeholder="Upload your profile picture"
                showCrop={true}
              />

              {/* Tips */}
              <div className="alert alert-info">
                <svg className="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                  <h4 className="font-medium">Tips for best results:</h4>
                  <ul className="text-sm mt-1 space-y-1">
                    <li>• Use a square image for best results</li>
                    <li>• Minimum resolution: 200x200 pixels</li>
                    <li>• File size should be under 5MB</li>
                    <li>• Supported formats: JPG, PNG, GIF, WebP</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="modal-action">
              <button
                type="button"
                onClick={() => setShowUploadModal(false)}
                className="btn btn-ghost rounded-full"
                disabled={isUploading}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
