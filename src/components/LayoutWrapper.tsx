"use client";

import { useState, useCallback } from "react";
import Navbar from "./navbar";
import Sidebar from "./Sidebar";
import Footer from "./Footer";

interface LayoutWrapperProps {
  children: React.ReactNode;
}

export default function LayoutWrapper({ children }: LayoutWrapperProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(prev => !prev);
  }, []);

  const closeSidebar = useCallback(() => {
    setIsSidebarOpen(false);
  }, []);

  return (
    <div className="min-h-screen bg-base-100 flex flex-col">
      {/* Navbar with sidebar toggle */}
      <Navbar onToggleSidebar={toggleSidebar} isSidebarOpen={isSidebarOpen} />

      <div className="relative flex-1 flex flex-col">
        {/* Unified Sidebar - Fixed positioned to slide over content */}
        <Sidebar isOpen={isSidebarOpen} onClose={closeSidebar} />

        {/* Main Content with dynamic margin and smooth transitions */}
        <main className={`flex-1 transition-all duration-300 ease-out ${
          isSidebarOpen ? 'lg:ml-80 lg:opacity-95' : 'lg:ml-0 lg:opacity-100'
        }`}>
          <div className="page-container">
            {children}
          </div>
        </main>

        {/* Footer */}
        <Footer />

        {/* Mobile Overlay with smooth fade */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden transition-opacity duration-300 animate-in fade-in"
            onClick={(e) => {
              e.stopPropagation();
              closeSidebar();
            }}
            style={{ animationDelay: '100ms' }}
          />
        )}
      </div>
    </div>
  );
}
