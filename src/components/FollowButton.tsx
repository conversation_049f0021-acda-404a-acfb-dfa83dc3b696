"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useFollowing } from "@/hooks/useFollowing";

interface FollowButtonProps {
  userId: string;
  username: string;
  isFollowing: boolean;
  followerCount: number;
  onFollowChange?: (userId: string, isFollowing: boolean, newCount: number) => void;
  size?: "sm" | "md" | "lg";
  variant?: "primary" | "outline" | "ghost";
  showCount?: boolean;
  className?: string;
}

export default function FollowButton({
  userId,
  username,
  isFollowing: initialIsFollowing,
  followerCount: initialFollowerCount,
  onFollowChange,
  size = "md",
  variant = "primary",
  showCount = false,
  className = ""
}: FollowButtonProps) {
  const { data: session, status } = useSession();
  // Include followingData so we can reconcile server/initial props with client follow state once loaded
  const { refreshFollowingData, followingData, isLoading: followingLoading } = useFollowing() as any;
  const [isFollowing, setIsFollowing] = useState(initialIsFollowing);
  const [followerCount, setFollowerCount] = useState(initialFollowerCount);
  const [isLoading, setIsLoading] = useState(false);
  const currentUserId = (session?.user as any)?.id;

  // Debug logging
  useEffect(() => {
    console.log(`FollowButton for user ${userId} (${username}): session status=${status}, session=${!!session}, initialIsFollowing=${initialIsFollowing}, currentIsFollowing=${isFollowing}`);
  }, [userId, username, status, session, initialIsFollowing, isFollowing]);

  // Update local state when parent prop changes (e.g., pagination re-render)
  useEffect(() => {
    setIsFollowing(initialIsFollowing);
    setFollowerCount(initialFollowerCount);
  }, [initialIsFollowing, initialFollowerCount]);

  // Reconcile with authoritative followingData once it finishes loading
  useEffect(() => {
    if (!followingLoading) {
      const rel = followingData?.get?.(userId);
      if (rel && rel.isFollowing !== isFollowing) {
        setIsFollowing(rel.isFollowing);
        // Only adjust follower count if we have a definite number and current differs
        if (typeof rel.followerCount === 'number' && rel.followerCount !== followerCount) {
          setFollowerCount(rel.followerCount);
        }
      }
    }
  }, [followingLoading, followingData, userId]);

  // Fallback: if still not marked following after hook loaded, directly check API once
  useEffect(() => {
    let aborted = false;
    const run = async () => {
      if (followingLoading || isFollowing) return; // already resolved
      if (status !== 'authenticated' || !session) return;
      try {
        const res = await fetch(`/api/users/${userId}/follow`, { cache: 'no-store' });
        if (!res.ok) return;
        const data = await res.json();
        if (!aborted && data?.isFollowing && !isFollowing) {
          setIsFollowing(true);
        }
      } catch (_) {}
    };
    run();
    return () => { aborted = true; };
  }, [followingLoading, status, session, userId, isFollowing]);

  // Prevent self-follow UI anomalies
  useEffect(() => {
    if (currentUserId && currentUserId === userId) {
      if (isFollowing) setIsFollowing(false);
    }
  }, [currentUserId, userId, isFollowing]);

  // Don't show follow button while session is loading
  if (status === "loading") {
    return (
      <div className="btn btn-ghost btn-sm rounded-full">
        <span className="loading loading-spinner loading-xs"></span>
      </div>
    );
  }

  // Don't show follow button for unauthenticated users or self
  if (status === "unauthenticated" || !session || (currentUserId && currentUserId === userId)) {
    return null;
  }

  // Check if it's the user's own profile by comparing with their email
  // We'll need to get the current user's ID from the database, but for now just check if they're authenticated
  // The real check will happen in the API when trying to follow

  const handleFollow = async () => {
    setIsLoading(true);
    
    try {
      console.log(`Follow button clicked for user ${userId}, current state: ${isFollowing}`);
      
      if (isFollowing) {
        // Unfollow user
        console.log(`Attempting to unfollow user ${userId}`);
        const response = await fetch(`/api/users/${userId}/follow`, {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          const error = await response.json();
          // If not following, just update the state to reflect reality
          if (error.error === 'Not following' || response.status === 404) {
            console.log(`User ${userId} is not being followed, updating state`);
            setIsFollowing(false);
            onFollowChange?.(userId, false, Math.max(0, followerCount - 1));
            return;
          }
          throw new Error(error.error || 'Failed to unfollow user');
        }
        
        console.log(`Successfully unfollowed user ${userId}`);
        setIsFollowing(false);
        setFollowerCount(prev => Math.max(0, prev - 1));
        onFollowChange?.(userId, false, Math.max(0, followerCount - 1));
        
        // Refresh following data to keep it in sync
        await refreshFollowingData();
      } else {
        // Follow user
        console.log(`Attempting to follow user ${userId}`);
        const response = await fetch(`/api/users/${userId}/follow`, {
          method: 'POST',
        });
        
        if (!response.ok) {
          const error = await response.json();
          // If already following, just update the state to reflect reality
          if (error.error === 'Already following') {
            console.log(`User ${userId} is already being followed, updating state`);
            setIsFollowing(true);
            onFollowChange?.(userId, true, followerCount);
            return;
          }
          throw new Error(error.error || 'Failed to follow user');
        }
        
        console.log(`Successfully followed user ${userId}`);
        setIsFollowing(true);
        setFollowerCount(prev => prev + 1);
        onFollowChange?.(userId, true, followerCount + 1);
        
        // Refresh following data to keep it in sync
        await refreshFollowingData();
      }
      
    } catch (error) {
      console.error("Failed to update follow status:", error);
      // Optionally show error message to user
    } finally {
      setIsLoading(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm": return "btn-sm text-xs px-3";
      case "lg": return "btn-lg text-base px-6";
      default: return "btn-md text-sm px-4";
    }
  };

  const getVariantClasses = () => {
    if (isFollowing) {
      switch (variant) {
        case "outline": return "btn-outline btn-primary hover:btn-error hover:text-error";
        case "ghost": return "btn-ghost text-primary hover:bg-error/10 hover:text-error";
        default: return "btn-primary hover:btn-error";
      }
    } else {
      switch (variant) {
        case "outline": return "btn-outline btn-primary";
        case "ghost": return "btn-ghost text-primary hover:bg-primary/10";
        default: return "btn-primary";
      }
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <button
        onClick={handleFollow}
        disabled={isLoading}
        className={`btn rounded-full transition-all duration-300 ${getSizeClasses()} ${getVariantClasses()}`}
      >
        {isLoading ? (
          <span className="loading loading-spinner loading-xs"></span>
        ) : (
          <>
            {isFollowing ? (
              <>
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="hidden sm:inline">Following</span>
                <span className="sm:hidden">✓</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span className="hidden sm:inline">Follow</span>
                <span className="sm:hidden">+</span>
              </>
            )}
          </>
        )}
      </button>
      
      {showCount && (
        <span className="text-sm text-base-content/70 font-medium">
          {followerCount.toLocaleString()} {followerCount === 1 ? 'follower' : 'followers'}
        </span>
      )}
    </div>
  );
}
