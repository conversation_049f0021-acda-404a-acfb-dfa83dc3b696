"use client";

interface ThemeSelectorProps {
  className?: string;
}

export default function ThemeSelector({ className = '' }: ThemeSelectorProps) {
  // Must match themes declared via @plugin "daisyui" in globals.css
  const themes = [
    { value: 'retro', label: 'Retro', icon: '🎨' },
    { value: 'jumpingdragon', label: 'Dragon', icon: '🐉' },
    { value: 'aqua', label: 'Aqua', icon: '�' },
    { value: 'forest', label: 'Forest', icon: '🌲' },
    { value: 'luxury', label: 'Luxury', icon: '✨' },
    { value: 'light', label: 'Light', icon: '☀️' },
    { value: 'dark', label: 'Dark', icon: '�' },
  ];

  return (
    <div className={`dropdown dropdown-end ${className}`}>
      <div 
        tabIndex={0} 
        role="button" 
        className="btn btn-ghost btn-circle hover:bg-primary/10 transition-all duration-300"
        title="Change theme"
      >
        <span className="text-lg">🎨</span>
        <svg
          width="12px"
          height="12px"
          className="inline-block h-2 w-2 fill-current opacity-60 ml-1"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 2048 2048"
        >
          <path d="M1799 349l242 241-1017 1017L7 590l242-241 775 775 775-775z"></path>
        </svg>
      </div>
      <ul 
        tabIndex={0} 
        className="dropdown-content bg-base-300 rounded-box z-[60] w-52 p-2 shadow-2xl mt-3"
      >
        <li className="menu-title px-3 py-2">
          <span className="text-xs font-semibold text-base-content/60 uppercase tracking-wider">
            Choose Theme
          </span>
        </li>
        {themes.map((theme) => (
          <li key={theme.value}>
            <input
              type="radio"
              name="theme-dropdown"
              className="theme-controller btn btn-sm btn-block btn-ghost justify-start"
              aria-label={theme.label}
              value={theme.value}
              defaultChecked={theme.value === 'retro'}
            />
          </li>
        ))}
      </ul>
    </div>
  );
}