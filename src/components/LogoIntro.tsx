"use client";

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';

interface LogoIntroProps {
  videoSrc: string;
  logoSrc: string;
  alt: string;
  className?: string;
  onVideoEnd?: () => void;
  /** Desired video banner height (px). Default 140 */
  heightPx?: number;
  /** Max width constraint for the banner. Default 1000 */
  maxWidthPx?: number;
  /** If true, ignore parent text container width and expand to viewport (capped). */
  fullWidth?: boolean;
  /** Max pixel width cap when fullWidth=true (default 1600) */
  maxVideoWidthPx?: number;
  /** Viewport width percentage when fullWidth=true (default 90) */
  viewportWidthPct?: number;
}

export default function LogoIntro({ 
  videoSrc, 
  logoSrc, 
  alt, 
  className = '',
  onVideoEnd,
  heightPx =400,
  maxWidthPx = 1000,
  fullWidth = false,
  maxVideoWidthPx = 1600,
  viewportWidthPct = 90
}: LogoIntroProps) {
  const [videoFinished, setVideoFinished] = useState(false);
  const [hideVideo, setHideVideo] = useState(false); // after fade-out completes
  const [isScrolled, setIsScrolled] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleVideoEnd = () => {
    // trigger blur + fade + scale-out
    setVideoFinished(true);
    // remove from layout after transition completes
    setTimeout(() => setHideVideo(true), 1100);
    onVideoEnd?.();
  };

  const handleVideoError = () => {
    console.warn('Video failed to load, showing static logo');
    setVideoFinished(true);
    setTimeout(() => setHideVideo(true), 10);
  };

  // Shared width style
  const widthStyle: React.CSSProperties = fullWidth ? {
    width: `min(${viewportWidthPct}vw, ${maxVideoWidthPx}px)`,
    maxWidth: '100%',
  } : {
    maxWidth: `${maxWidthPx}px`,
    width: '100%'
  };

  return (
    <div className={`relative ${className}`} style={widthStyle}>
      <div className="relative w-full mx-auto flex items-center justify-center" style={{ height: `${heightPx}px` }}>
        {/* Video layer */}
        {!hideVideo && (
          <video
            ref={videoRef}
            autoPlay
            muted
            playsInline
            onEnded={handleVideoEnd}
            onError={handleVideoError}
            className={`block w-full object-contain transition-all duration-1000 ease-out will-change-transform will-change-filter ${videoFinished ? 'opacity-0 blur-md scale-105' : 'opacity-100 blur-0 scale-100'}`}
            style={{ height: `${heightPx}px`, backgroundColor: 'transparent' }}
          >
            <source src="/video/logo-transparent.webm" type="video/webm" />
            <source src={videoSrc.replace('.mkv', '.webm').replace('.mov', '.webm')} type="video/webm" />
            <source src={videoSrc.replace('.mkv', '.mp4').replace('.mov', '.mp4')} type="video/mp4" />
            <source src={videoSrc} type="video/x-matroska" />
            Your browser does not support the video tag.
          </video>
        )}

        {/* Static logo cross-fade layer */}
        <div className={`absolute inset-0 flex items-center justify-center transition-all duration-700 ease-out ${videoFinished ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
             aria-hidden={!videoFinished}>
          <div className="relative">
            <div className={`relative transition-all duration-700 ease-out hover:scale-105 ${
                isScrolled
                  ? 'w-16 h-16 opacity-0 scale-75'
                  : 'w-48 h-48 md:w-56 md:h-56 opacity-100 scale-100'
              }`}>
              <Image
                src={logoSrc}
                alt={alt}
                fill
                priority
                className="object-contain"
              />
            </div>
            <div className={`absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transition-all duration-700 ease-out hover:scale-105 ${
                isScrolled
                  ? 'w-16 h-16 opacity-100 scale-100'
                  : 'w-12 h-12 opacity-0 scale-75'
              }`}>
              <Image
                src="/favicon.png"
                alt="COMFOR Favicon"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
