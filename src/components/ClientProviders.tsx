"use client";

import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";
import { RealtimeProvider } from "./RealtimeProvider";

interface ClientProvidersProps {
  children: ReactNode;
}

export function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <SessionProvider
      refetchInterval={5 * 60}
      refetchOnWindowFocus={true}
      basePath="/api/auth"
    >
      <RealtimeProvider>
        {children}
      </RealtimeProvider>
    </SessionProvider>
  );
}