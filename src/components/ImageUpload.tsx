"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { useDropzone } from "react-dropzone";

interface ImageUploadProps {
  onImageSelect: (file: File, preview: string) => void;
  onImageRemove?: () => void;
  currentImage?: string;
  maxSize?: number; // in MB
  aspectRatio?: number; // width/height ratio for cropping
  shape?: "square" | "circle" | "rectangle";
  className?: string;
  placeholder?: string;
  showCrop?: boolean;
}

export default function ImageUpload({
  onImageSelect,
  onImageRemove,
  currentImage,
  maxSize = 5,
  aspectRatio = 1,
  shape = "square",
  className = "",
  placeholder = "Click to upload or drag and drop",
  showCrop = true
}: ImageUploadProps) {
  const [preview, setPreview] = useState<string | null>(currentImage || null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCropModal, setShowCropModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  // Cropping state (interactive pan & zoom)
  const cropSize = 300; // base crop viewport (square)
  const [imageDims, setImageDims] = useState<{ width: number; height: number } | null>(null);
  const [baseScale, setBaseScale] = useState(1); // minimum scale that covers crop area
  const [scaleMultiplier, setScaleMultiplier] = useState(1); // user-controlled multiplier (1..max)
  const [offset, setOffset] = useState({ x: 0, y: 0 }); // position of top-left of scaled image relative to crop frame
  const [dragging, setDragging] = useState(false);
  const dragStartRef = useRef<{ x: number; y: number; offsetX: number; offsetY: number } | null>(null);
  const MAX_SCALE_MULTIPLIER = 3; // allow up to 3x zoom beyond base

  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return 'Please select an image file';
    }

    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      return `File size must be less than ${maxSize}MB`;
    }

    // Check image dimensions (optional)
    return null;
  };

  const createPreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.readAsDataURL(file);
    });
  };

  const handleFileSelect = async (files: File[]) => {
    const file = files[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError(null);
    setSelectedFile(file);
    
    const previewUrl = await createPreview(file);
    setPreview(previewUrl);

    if (showCrop) {
      setShowCropModal(true);
    } else {
      onImageSelect(file, previewUrl);
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    handleFileSelect(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: false,
    maxSize: maxSize * 1024 * 1024
  });

  const clampOffsets = (imgW: number, imgH: number, scale: number, x: number, y: number) => {
    const scaledW = imgW * scale;
    const scaledH = imgH * scale;
    const minX = Math.min(0, cropSize - scaledW);
    const minY = Math.min(0, cropSize - scaledH);
    const clampedX = Math.max(minX, Math.min(0, x));
    const clampedY = Math.max(minY, Math.min(0, y));
    return { x: clampedX, y: clampedY };
  };

  // Initialize image dimensions & base scale when preview ready
  useEffect(() => {
    if (!showCropModal || !preview) return;
    const img = new Image();
    img.onload = () => {
      const { width, height } = img;
      setImageDims({ width, height });
      // Determine minimum scale so image fully covers crop square
      const minScale = Math.max(cropSize / width, cropSize / height);
      setBaseScale(minScale);
      setScaleMultiplier(1);
      // Center image
      const scaledW = width * minScale;
      const scaledH = height * minScale;
      setOffset({ x: (cropSize - scaledW) / 2, y: (cropSize - scaledH) / 2 });
    };
    img.src = preview;
  }, [showCropModal, preview]);

  // Re-clamp offsets when scale changes
  useEffect(() => {
    if (!imageDims) return;
    const scale = baseScale * scaleMultiplier;
    setOffset(prev => clampOffsets(imageDims.width, imageDims.height, scale, prev.x, prev.y));
  }, [scaleMultiplier, baseScale, imageDims]);

  const handleDragStart = (clientX: number, clientY: number) => {
    if (!imageDims) return;
    dragStartRef.current = { x: clientX, y: clientY, offsetX: offset.x, offsetY: offset.y };
    setDragging(true);
  };

  const handleDragMove = (clientX: number, clientY: number) => {
    if (!dragStartRef.current || !imageDims) return;
    const { x, y, offsetX, offsetY } = dragStartRef.current;
    const dx = clientX - x;
    const dy = clientY - y;
    const scale = baseScale * scaleMultiplier;
    const newOffset = clampOffsets(imageDims.width, imageDims.height, scale, offsetX + dx, offsetY + dy);
    setOffset(newOffset);
  };

  const handleDragEnd = () => {
    setDragging(false);
    dragStartRef.current = null;
  };

  const handleCropConfirm = async () => {
    if (!selectedFile || !canvasRef.current || !imageDims) return;
    setIsUploading(true);
    try {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      const { width: imgW, height: imgH } = imageDims;
      const scale = baseScale * scaleMultiplier;
      const cropX = -offset.x / scale; // translate back to original coords
      const cropY = -offset.y / scale;
      const cropWidth = cropSize / scale;
      const cropHeight = cropSize / scale;
      canvas.width = cropSize;
      canvas.height = cropSize;
      const img = new Image();
      img.onload = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, cropX, cropY, cropWidth, cropHeight, 0, 0, cropSize, cropSize);
        canvas.toBlob((blob) => {
          if (blob) {
            const croppedFile = new File([blob], selectedFile.name, { type: selectedFile.type, lastModified: Date.now() });
            const croppedPreview = canvas.toDataURL();
            setPreview(croppedPreview);
            onImageSelect(croppedFile, croppedPreview);
          }
        }, selectedFile.type, 0.92);
        setShowCropModal(false);
      };
      img.src = preview!;
    } catch (err) {
      console.error(err);
      setError('Failed to process image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemove = () => {
    setPreview(null);
    setSelectedFile(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onImageRemove?.();
  };

  const getShapeClasses = () => {
    switch (shape) {
      case "circle":
        return "rounded-full";
      case "rectangle":
        return "rounded-xl";
      default:
        return "rounded-2xl";
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed transition-all duration-300 cursor-pointer
          ${isDragActive 
            ? 'border-primary bg-primary/5' 
            : 'border-base-300 hover:border-primary/50 hover:bg-base-200/50'
          }
          ${getShapeClasses()}
          ${preview ? 'p-2' : 'p-8'}
        `}
      >
        <input {...getInputProps()} ref={fileInputRef} />
        
        {preview ? (
          <div className="relative group">
            <img
              src={preview}
              alt="Preview"
              className={`w-full h-full object-cover ${getShapeClasses()}`}
              style={{ aspectRatio }}
            />
            
            {/* Overlay */}
            <div className={`
              absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 
              transition-opacity duration-300 flex items-center justify-center space-x-2
              ${getShapeClasses()}
            `}>
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  fileInputRef.current?.click();
                }}
                className="btn btn-sm btn-primary rounded-full"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </button>
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemove();
                }}
                className="btn btn-sm btn-error rounded-full"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>
        ) : (
          <div className="text-center">
            <svg className="mx-auto h-12 w-12 text-base-content/40 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-base-content/70 mb-2">{placeholder}</p>
            <p className="text-xs text-base-content/50">
              PNG, JPG, GIF up to {maxSize}MB
            </p>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="alert alert-error">
          <svg className="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span>{error}</span>
        </div>
      )}

      {/* Crop Modal */}
      {showCropModal && (
        <div className="modal modal-open">
          <div className="modal-box max-w-4xl">
            <h3 className="font-bold text-lg mb-4">Crop Image</h3>
            
            <div className="space-y-4">
              {/* Crop Preview */}
              <div className="space-y-4">
                {/* Interactive Crop Area */}
                <div
                  className={`relative mx-auto bg-base-200 flex items-center justify-center ${shape === 'circle' ? 'rounded-full' : 'rounded-xl'} shadow-inner`}
                  style={{ width: cropSize, height: cropSize, overflow: 'hidden', touchAction: 'none', cursor: dragging ? 'grabbing' : 'grab' }}
                  onMouseDown={(e) => { e.preventDefault(); handleDragStart(e.clientX, e.clientY); }}
                  onMouseMove={(e) => { if (dragging) handleDragMove(e.clientX, e.clientY); }}
                  onMouseUp={handleDragEnd}
                  onMouseLeave={() => dragging && handleDragEnd()}
                  onTouchStart={(e) => { const t = e.touches[0]; handleDragStart(t.clientX, t.clientY); }}
                  onTouchMove={(e) => { const t = e.touches[0]; handleDragMove(t.clientX, t.clientY); }}
                  onTouchEnd={handleDragEnd}
                >
                  {preview && imageDims && (
                    <img
                      src={preview}
                      alt="Crop"
                      draggable={false}
                      style={{
                        position: 'absolute',
                        left: offset.x,
                        top: offset.y,
                        width: imageDims.width * baseScale * scaleMultiplier,
                        height: imageDims.height * baseScale * scaleMultiplier,
                        userSelect: 'none',
                        pointerEvents: 'none'
                      }}
                    />
                  )}
                  {/* Optional overlay grid */}
                  <div className="absolute inset-0 pointer-events-none" style={{ backgroundImage: 'linear-gradient(transparent, transparent 32%), linear-gradient(90deg, transparent, transparent 32%)', backgroundSize: '33% 33%', opacity: 0.15 }} />
                </div>

                {/* Scale Slider */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Zoom</span>
                    <span className="text-xs opacity-60">{scaleMultiplier.toFixed(2)}x</span>
                  </label>
                  <input
                    type="range"
                    min={1}
                    max={MAX_SCALE_MULTIPLIER}
                    step={0.01}
                    value={scaleMultiplier}
                    onChange={(e) => setScaleMultiplier(parseFloat(e.target.value))}
                    className="range range-primary"
                  />
                </div>
                <p className="text-xs text-base-content/60">Drag the image to position. Use zoom to adjust framing.</p>
              </div>
            </div>

            <div className="modal-action">
              <button
                type="button"
                onClick={() => setShowCropModal(false)}
                className="btn btn-ghost rounded-full"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleCropConfirm}
                disabled={isUploading}
                className="btn btn-primary rounded-full"
              >
                {isUploading ? (
                  <>
                    <span className="loading loading-spinner loading-sm mr-2"></span>
                    Processing...
                  </>
                ) : (
                  'Confirm'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Hidden canvas for cropping */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
}
