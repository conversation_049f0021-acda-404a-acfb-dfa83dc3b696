// Utility helpers for safely extracting dynamic route params in App Router client components.
// Next.js useParams() generally returns a plain object, but framework/type evolution or edge cases
// (e.g. during fast refresh) could yield unexpected shapes. These helpers defensively coerce.

export function getRouteParam(params: unknown, key: string): string {
  if (!params || typeof params !== 'object') return '';
  const value = (params as Record<string, unknown>)[key];
  return typeof value === 'string' ? value : '';
}

export function getRouteParams(params: unknown): Record<string, string> {
  if (!params || typeof params !== 'object') return {};
  const out: Record<string, string> = {};
  for (const [k, v] of Object.entries(params as Record<string, unknown>)) {
    if (typeof v === 'string') out[k] = v;
  }
  return out;
}
