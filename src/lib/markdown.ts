import { marked } from 'marked';
import DOMPurify from 'isomorphic-dompurify';

// Configure marked for basic GitHub-like markdown
marked.setOptions({
  breaks: true,
  gfm: true,
});

export function renderMarkdown(raw: string): string {
  if (!raw) return '';
  const html = marked.parse(raw);
  // Sanitize to prevent XSS
  return DOMPurify.sanitize(html as string, { USE_PROFILES: { html: true } });
}
