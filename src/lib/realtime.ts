import type { Server as IOServer } from 'socket.io';
import { Emitter } from '@socket.io/redis-emitter';
import Redis from 'ioredis';

let emitter: any | null = null;
function getEmitter(): any | null {
  if (emitter) return emitter;
  // Lazy init only if REDIS_URL present (avoid bundling in client). This module should only run server-side.
  const url = process.env.REDIS_URL;
  if (!url) return null;
  try {
    const redis = new Redis(url, { lazyConnect: false });
    emitter = new Emitter(redis as any);
    return emitter;
  } catch (e) {
    console.warn('[realtime] failed to init redis emitter', (e as any)?.message);
    return null;
  }
}

// Access the (optional) global Socket.IO instance if a custom server wired it.
declare const globalThis: { _io?: IOServer } & typeof global;

export function getIo(): IOServer | undefined {
  return globalThis._io;
}

export function emitPostCreated(payload: any) {
  const io = getIo();
  if (io) io.emit('post:new', payload);
  else getEmitter()?.emit('post:new', payload);
}

export function emitNotification(userId: string | undefined, data: any) {
  const io = getIo();
  if (io) {
    if (userId) io.to(`user:${userId}`).emit('notification', data);
    else io.emit('notification', data);
    return;
  }
  const em = getEmitter();
  if (!em) return;
  if (userId) em.to(`user:${userId}`).emit('notification', data);
  else em.emit('notification', data);
}

// No-ops if the server instance does not exist (e.g. Vercel serverless without custom server).