import Redis, { RedisOptions } from 'ioredis';

let client: Redis | null = null;

export function getRedis(): Redis | null {
  const url = process.env.REDIS_URL;
  const password = process.env.REDIS_PASSWORD || undefined;
  if (!url) return null;
  if (!client) {
    try {
      // If password not embedded in URL and provided separately, pass via options
      const needsPasswordOption = password && !/redis:\/\/.*@/.test(url);
      const options: RedisOptions | undefined = needsPasswordOption ? { password } : undefined;
      client = options ? new Redis(url, options) : new Redis(url);
      client.on('error', (err) => {
        // Swallow auth errors gracefully so feature degradation (like view counts) doesn't crash app
        if (process.env.NODE_ENV !== 'production') {
          console.warn('[redis] error', err.message);
        }
      });
    } catch (e) {
      if (process.env.NODE_ENV !== 'production') {
        console.warn('[redis] init failed:', (e as <PERSON>rror).message);
      }
      return null;
    }
  }
  return client;
}

export function viewsKey(postId: string) {
  return `post:views:${postId}`;
}
