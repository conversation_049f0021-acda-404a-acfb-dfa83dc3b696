import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import GitHubProvider from "next-auth/providers/github";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";

// Demo users for testing
const demoUsers = [
  {
    id: "1",
    email: "<EMAIL>",
    name: "Admin",
    role: "ADMIN",
    image: null,
  },
  {
    id: "2",
    email: "<EMAIL>",
    name: "Editor",
    role: "EDITOR",
    image: null,
  },
  {
    id: "3",
    email: "<EMAIL>",
    name: "User",
    role: "USER",
    image: null,
  },
];

export const authOptions: NextAuthOptions = {
  // Dynamically configure cookie security based on environment
  cookies: {
    sessionToken: {
      name: (() => {
        const secure = process.env.SECURE_COOKIES === 'true';
        // Use explicit env name if provided; otherwise choose prefix for secure envs
        const wanted = process.env.SESSION_COOKIE_NAME;
        if (wanted) return wanted;
        return secure ? '__Host_forum_session' : 'dev_forum_session';
      })(),
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        // Only mark secure if running behind HTTPS / user requested SECURE_COOKIES
        secure: process.env.SECURE_COOKIES === 'true'
      }
    }
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // First check demo users for backward compatibility
          const demoUser = demoUsers.find(u => u.email === credentials.email);
          if (demoUser) {
            // For demo users, accept any password
            return {
              id: demoUser.id,
              email: demoUser.email,
              name: demoUser.name,
              image: demoUser.image,
              role: demoUser.role,
            };
          }

          // Check database for registered users
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
            select: {
              id: true,
              email: true,
              name: true,
              image: true,
              role: true,
              password: true,
            },
          });

          if (!user || !user.password) {
            return null;
          }

          // Verify password
          const isPasswordValid = await bcrypt.compare(credentials.password, user.password);
          if (!isPasswordValid) {
            return null;
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            role: user.role,
          };
        } catch (error) {
          console.error('Authentication error:', error);
          return null;
        }
      }
    }),
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      })
    ] : []),
    ...(process.env.GITHUB_CLIENT_ID && process.env.GITHUB_CLIENT_SECRET ? [
      GitHubProvider({
        clientId: process.env.GITHUB_CLIENT_ID,
        clientSecret: process.env.GITHUB_CLIENT_SECRET,
      })
    ] : []),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
  if (user) {
    // Use NextAuth's User type or define a custom type for user
    type AppUser = {
      id: string;
      role: string;
    };
    const appUser = user as AppUser;
    token.role = appUser.role;
    // Preserve user id explicitly (NextAuth uses sub, but we also mirror to uid for clarity)
    token.uid = appUser.id || token.sub;
  }
      return token;
    },
  async session({ session, token }) {
    if (session.user) {
      type SessionUser = {
        id?: string;
        role?: string;
        name?: string | null;
        email?: string | null;
        image?: string | null;
      };
      const user = session.user as SessionUser;
      user.id = (token as { uid?: string; sub?: string }).uid || token.sub;
      user.role = token.role as string;
    }
    return session;
  },
  },
  pages: {
    signIn: "/auth/signin",
  },
};
