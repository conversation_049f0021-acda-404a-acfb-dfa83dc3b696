"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import Link from "next/link";
import { useFollowing } from "@/hooks/useFollowing";

interface ActivityItem {
  id: string;
  type: "post" | "comment" | "like" | "follow";
  user: {
    id: string;
    name: string;
    username: string;
    avatar: string;
  };
  content?: string;
  postTitle?: string;
  postId?: string;
  targetUser?: {
    name: string;
    username: string;
  };
  timestamp: Date;
  isRead: boolean;
}

export default function FeedPage() {
  const { data: session } = useSession();
  const { followingData } = useFollowing();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch recent posts from followed users as activity
  useEffect(() => {
    const fetchFeedData = async () => {
      if (!session?.user || followingData.size === 0) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetch('/api/posts?published=true&limit=20');
        
        if (response.ok) {
          const data = await response.json();
          const followedUserIds = new Set(Array.from(followingData.keys()));
          
          const postActivities: ActivityItem[] = data.posts
            .filter((post: any) => followedUserIds.has(post.author.id))
            .map((post: any) => ({
              id: `post-${post.id}`,
              type: "post" as const,
              user: {
                id: post.author.id,
                name: post.author.name,
                username: post.author.email?.split('@')[0] || post.author.id,
                avatar: post.author.image || ''
              },
              content: post.content.substring(0, 200) + (post.content.length > 200 ? '...' : ''),
              postTitle: post.title,
              postId: post.id,
              timestamp: new Date(post.createdAt),
              isRead: true
            }));

          setActivities(postActivities);
        }
      } catch (error) {
        console.error('Error fetching feed data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeedData();
  }, [session, followingData]);

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30 flex items-center justify-center">
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body text-center p-12">
            <h2 className="text-2xl font-bold mb-4">Sign In Required</h2>
            <p className="text-base-content/70 mb-6">Please sign in to view your activity feed.</p>
            <Link href="/auth/signin" className="btn btn-primary rounded-full">
              Sign In
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
      <div className="max-w-4xl mx-auto space-y-8 p-4">
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body p-8">
            <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-2">
              Activity Feed
            </h1>
            <p className="text-base-content/70 text-lg">
              Stay updated with activity from people you follow
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {loading ? (
            <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
              <div className="card-body text-center py-16">
                <span className="loading loading-spinner loading-lg text-primary"></span>
                <p className="text-base-content/70 mt-4">Loading your activity feed...</p>
              </div>
            </div>
          ) : activities.length === 0 ? (
            <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
              <div className="card-body text-center py-16">
                <div className="text-6xl mb-4">📱</div>
                <h3 className="text-2xl font-bold mb-2">No activity found</h3>
                <p className="text-base-content/70 mb-6">
                  {followingData.size === 0 
                    ? "Follow some users to see their activity in your feed!" 
                    : "No recent activity from people you follow."}
                </p>
                <Link href="/users" className="btn btn-primary rounded-full">
                  Discover Users
                </Link>
              </div>
            </div>
          ) : (
            activities.map((activity) => (
              <div
                key={activity.id}
                className="card border bg-gradient-to-br from-base-100 to-base-200/30 border-base-300/40 shadow transition-all duration-200 hover:shadow-lg hover:border-primary/30"
              >
                <div className="card-body p-4 flex flex-col sm:flex-row items-start gap-4">
                  <div className="avatar flex-shrink-0">
                    <div className="w-10 h-10 rounded-full ring-2 ring-primary/20 overflow-hidden">
                      <img src={activity.user.avatar} alt={activity.user.name} className="object-cover w-10 h-10" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Link href={`/users/${activity.user.username}`} className="font-semibold text-base hover:text-primary">
                        {activity.user.name}
                      </Link>
                      <span className="text-xs text-base-content/60">• {activity.timestamp.toLocaleDateString()}</span>
                    </div>
                    <div className="text-sm text-base-content/80 mb-2">
                      created a new post
                      {activity.postTitle && (
                        <Link href={`/posts/${activity.postId}`} className="text-primary font-medium hover:underline ml-1">
                          {activity.postTitle}
                        </Link>
                      )}
                    </div>
                    {activity.content && (
                      <div className="bg-base-200/50 rounded-lg p-2">
                        <p className="text-xs text-base-content/70 line-clamp-2">{activity.content}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
