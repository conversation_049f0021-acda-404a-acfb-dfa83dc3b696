import Link from 'next/link';

export default function NotFound() {
  return (
    <main className="min-h-dvh flex flex-col items-center justify-center px-6 py-12 text-center gap-6">
      <div>
        <h1 className="text-7xl font-extrabold tracking-tight">
          <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">404</span>
        </h1>
        <p className="mt-4 text-2xl font-semibold">Page Not Found</p>
        <p className="mt-3 opacity-70 max-w-md mx-auto">
          The page you were looking for doesn&apos;t exist or was moved. Check the URL or head back home.
        </p>
      </div>
      <div className="flex gap-4">
        <Link href="/" className="btn btn-primary">Go Home</Link>
        <Link href="/search" className="btn btn-outline">Search</Link>
      </div>
    </main>
  );
}
