"use client";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <body>
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold mb-4">Something went wrong!</h2>
          <button onClick={reset} className="btn btn-primary">
            Try again
          </button>
        </div>
      </div>
    </body>
  );
}