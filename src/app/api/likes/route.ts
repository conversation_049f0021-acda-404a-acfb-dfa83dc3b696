import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for likes
const likeSchema = z.object({
  postId: z.string().optional(),
  commentId: z.string().optional(),
}).refine(
  (data) => data.postId || data.commentId,
  {
    message: "Either postId or commentId must be provided",
  }
).refine(
  (data) => !(data.postId && data.commentId),
  {
    message: "Cannot like both post and comment at the same time",
  }
);

// POST /api/likes - Toggle like on post or comment
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = likeSchema.parse(body);

    // Find the user in the database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if the target (post or comment) exists
    if (validatedData.postId) {
      const post = await prisma.post.findUnique({
        where: { id: validatedData.postId },
      });

      if (!post) {
        return NextResponse.json(
          { error: 'Post not found' },
          { status: 404 }
        );
      }
    }

    if (validatedData.commentId) {
      const comment = await prisma.comment.findUnique({
        where: { id: validatedData.commentId },
      });

      if (!comment) {
        return NextResponse.json(
          { error: 'Comment not found' },
          { status: 404 }
        );
      }
    }

    // Check if like already exists
    const existingLike = await prisma.like.findFirst({
      where: {
        userId: user.id,
        ...(validatedData.postId && { postId: validatedData.postId }),
        ...(validatedData.commentId && { commentId: validatedData.commentId }),
      },
    });

    if (existingLike) {
      // Unlike - remove the like
      await prisma.like.delete({
        where: { id: existingLike.id },
      });

      // Get updated count
      const count = await prisma.like.count({
        where: {
          ...(validatedData.postId && { postId: validatedData.postId }),
          ...(validatedData.commentId && { commentId: validatedData.commentId }),
        },
      });

      return NextResponse.json({
        liked: false,
        count,
        message: 'Like removed successfully',
      });
    } else {
      // Like - create new like
      const like = await prisma.like.create({
        data: {
          userId: user.id,
          postId: validatedData.postId,
          commentId: validatedData.commentId,
        },
      });

      // Get updated count
      const count = await prisma.like.count({
        where: {
          ...(validatedData.postId && { postId: validatedData.postId }),
          ...(validatedData.commentId && { commentId: validatedData.commentId }),
        },
      });

      return NextResponse.json({
        liked: true,
        count,
        like,
        message: 'Like added successfully',
      }, { status: 201 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error toggling like:', error);
    return NextResponse.json(
      { error: 'Failed to toggle like' },
      { status: 500 }
    );
  }
}

// GET /api/likes - Get like status for user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const postId = searchParams.get('postId');
    const commentId = searchParams.get('commentId');

    if (!postId && !commentId) {
      return NextResponse.json(
        { error: 'Either postId or commentId is required' },
        { status: 400 }
      );
    }

    // Find the user in the database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has liked the post/comment
    const like = await prisma.like.findFirst({
      where: {
        userId: user.id,
        ...(postId && { postId }),
        ...(commentId && { commentId }),
      },
    });

    // Get total like count
    const count = await prisma.like.count({
      where: {
        ...(postId && { postId }),
        ...(commentId && { commentId }),
      },
    });

    return NextResponse.json({
      liked: !!like,
      count,
    });
  } catch (error) {
    console.error('Error fetching like status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch like status' },
      { status: 500 }
    );
  }
}
