import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/stats/summary - aggregate counters for homepage (lightweight)
export async function GET() {
  try {
    const [userCount, communityCount, postCount] = await Promise.all([
      prisma.user.count(),
      prisma.community.count(),
      prisma.post.count({ where: { published: true } })
    ]);
    return NextResponse.json({ userCount, communityCount, postCount });
  } catch (error) {
    console.error('Error fetching stats summary:', error);
    return NextResponse.json({ userCount: 0, communityCount: 0, postCount: 0 }, { status: 500 });
  }
}
