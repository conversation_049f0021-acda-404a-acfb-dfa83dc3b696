import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/followers - Get users who follow the current user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const skip = (page - 1) * limit;

    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Build where clause for followers
    const where: any = { followingId: currentUser.id };
    
    if (search) {
      where.follower = {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { bio: { contains: search, mode: 'insensitive' } },
        ],
      };
    }

    const [followers, total] = await Promise.all([
      prisma.follow.findMany({
        where,
        include: {
          follower: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              bio: true,
              role: true,
              createdAt: true,
              isPrivate: true,
              showEmail: true,
              _count: {
                select: {
                  posts: true,
                  followers: true,
                  following: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.follow.count({ where }),
    ]);

    // Check which followers the current user is following back
    const followerIds = followers.map(f => f.follower.id);
    const followingBack = await prisma.follow.findMany({
      where: {
        followerId: currentUser.id,
        followingId: { in: followerIds },
      },
      select: { followingId: true },
    });

    const followingBackSet = new Set(followingBack.map(f => f.followingId));

    const followersWithMutualStatus = followers.map(follow => ({
      ...follow.follower,
      followedAt: follow.createdAt,
      isFollowing: followingBackSet.has(follow.follower.id),
      // Apply privacy controls
      email: follow.follower.showEmail || follow.follower.isPrivate === false ? follow.follower.email : undefined,
    }));

    return NextResponse.json({
      followers: followersWithMutualStatus,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching followers:', error);
    return NextResponse.json({ error: 'Failed to fetch followers' }, { status: 500 });
  }
}
