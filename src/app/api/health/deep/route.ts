import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getRedis } from '@/lib/redis';

interface ServiceStatus { [k: string]: unknown }
interface HealthDetails {
  database?: { status: string; latencyMs?: number; error?: string };
  redis?: { status: string; reply?: string; error?: string };
  auth?: { status: string; hasUrl?: boolean; hasSecret?: boolean };
  [k: string]: ServiceStatus | undefined;
}

// Deep health check covering multiple subsystems (database, redis, auth config)
// NOTE: Avoids performing an actual auth session fetch to keep it lightweight.
export async function GET() {
  const started = Date.now();
  const details: HealthDetails = {};
  let unhealthy = false;

  // Database check
  try {
    await prisma.$queryRaw`SELECT 1`;
    details.database = { status: 'up', latencyMs: 0 }; // fill after measuring
  } catch (e) {
    unhealthy = true;
    details.database = { status: 'down', error: e instanceof Error ? e.message : 'unknown' };
  }

  // Redis check (optional)
  try {
    const redis = getRedis();
    if (!redis) {
      details.redis = { status: 'disabled' };
    } else {
      // PING with timeout guard
      const pingResult = await Promise.race([
        redis.ping(),
        new Promise<string>((_, reject) => setTimeout(() => reject(new Error('timeout')), 1000))
      ]);
      details.redis = { status: 'up', reply: pingResult };
    }
  } catch (e) {
    unhealthy = true;
    details.redis = { status: 'down', error: e instanceof Error ? e.message : 'unknown' };
  }

  // Auth env sanity (do not expose secrets)
  const hasUrl = !!process.env.NEXTAUTH_URL;
  const hasSecret = !!process.env.NEXTAUTH_SECRET;
  if (!hasUrl || !hasSecret) {
    details.auth = { status: 'misconfigured', hasUrl, hasSecret };
    // Treat missing auth config as unhealthy in production only
    if (process.env.NODE_ENV === 'production') unhealthy = true;
  } else {
    details.auth = { status: 'configured' };
  }

  // Compute simple latency metrics
  const totalMs = Date.now() - started;
  if (details.database && details.database.status === 'up') {
    details.database.latencyMs = totalMs;
  }

  const body = {
    status: unhealthy ? 'unhealthy' : 'healthy',
    timestamp: new Date().toISOString(),
    uptimeSeconds: process.uptime(),
    totalLatencyMs: totalMs,
    services: details,
    version: process.env.APP_VERSION || 'unknown'
  };

  return NextResponse.json(body, { status: unhealthy ? 503 : 200 });
}
