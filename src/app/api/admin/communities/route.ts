import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for community updates
const updateCommunitySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  image: z.string().optional(),
  isPrivate: z.boolean().optional(),
  allowPosts: z.boolean().optional(),
  requireApproval: z.boolean().optional(),
});

// Validation schema for bulk actions
const bulkActionSchema = z.object({
  action: z.enum(['delete', 'archive', 'unarchive', 'private', 'public']),
  communityIds: z.array(z.string()).min(1),
});

// GET /api/admin/communities - Get all communities with admin details
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const status = searchParams.get('status'); // active, archived, all
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status === 'archived') {
      where.archived = true;
    } else if (status === 'active') {
      where.archived = false;
    }

    // Get communities with detailed information
    const [communities, total] = await Promise.all([
      prisma.community.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              image: true,
            },
          },
          _count: {
            select: {
              posts: true,
              members: true,
            },
          },
          posts: {
            select: {
              id: true,
              title: true,
              createdAt: true,
              published: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 3,
          },
          members: {
            select: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true,
                },
              },
              role: true,
              joinedAt: true,
            },
            orderBy: {
              joinedAt: 'desc',
            },
            take: 5,
          },
        },
        orderBy: {
          [sortBy]: sortOrder as 'asc' | 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.community.count({ where }),
    ]);

    // Get additional statistics
    const stats = {
      totalCommunities: total,
      activeCommunities: await prisma.community.count({ where: { archived: false } }),
      archivedCommunities: await prisma.community.count({ where: { archived: true } }),
      privateCommunities: await prisma.community.count({ where: { isPrivate: true } }),
      communitiesToday: await prisma.community.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
    };

    return NextResponse.json({
      communities,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats,
    });
  } catch (error) {
    console.error('Error fetching admin communities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch communities' },
      { status: 500 }
    );
  }
}

// POST /api/admin/communities - Bulk actions on communities
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, communityIds } = bulkActionSchema.parse(body);

    // Verify all communities exist
    const communities = await prisma.community.findMany({
      where: { id: { in: communityIds } },
      select: { id: true, name: true },
    });

    if (communities.length !== communityIds.length) {
      return NextResponse.json(
        { error: 'Some communities not found' },
        { status: 404 }
      );
    }

    let updateData: any = {};
    let successMessage = '';

    switch (action) {
      case 'archive':
        updateData = { archived: true };
        successMessage = `Archived ${communityIds.length} communities`;
        break;
      case 'unarchive':
        updateData = { archived: false };
        successMessage = `Unarchived ${communityIds.length} communities`;
        break;
      case 'private':
        updateData = { isPrivate: true };
        successMessage = `Made ${communityIds.length} communities private`;
        break;
      case 'public':
        updateData = { isPrivate: false };
        successMessage = `Made ${communityIds.length} communities public`;
        break;
      case 'delete':
        await prisma.community.deleteMany({
          where: { id: { in: communityIds } },
        });
        return NextResponse.json({
          message: `Deleted ${communityIds.length} communities`,
          deletedIds: communityIds,
        });
    }

    // Update communities
    const result = await prisma.community.updateMany({
      where: { id: { in: communityIds } },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      message: successMessage,
      updatedCount: result.count,
      updatedIds: communityIds,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error performing bulk action:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk action' },
      { status: 500 }
    );
  }
}
