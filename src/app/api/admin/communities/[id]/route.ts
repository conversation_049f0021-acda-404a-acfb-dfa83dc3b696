import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for community updates
const updateCommunitySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  image: z.string().optional(),
  isPrivate: z.boolean().optional(),
  allowPosts: z.boolean().optional(),
  requireApproval: z.boolean().optional(),
  archived: z.boolean().optional(),
});

// GET /api/admin/communities/[id] - Get detailed community information
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get detailed community information
    const community = await prisma.community.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            image: true,
            createdAt: true,
          },
        },
        posts: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
                image: true,
              },
            },
            _count: {
              select: {
                comments: true,
                likes: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
                image: true,
              },
            },
          },
          orderBy: {
            joinedAt: 'desc',
          },
          take: 20,
        },
        _count: {
          select: {
            posts: true,
            members: true,
          },
        },
      },
    });

    if (!community) {
      return NextResponse.json(
        { error: 'Community not found' },
        { status: 404 }
      );
    }

    // Get additional analytics
    const analytics = {
      postsThisWeek: await prisma.post.count({
        where: {
          communityId: id,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      newMembersThisWeek: await prisma.communityMember.count({
        where: {
          communityId: id,
          joinedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      topContributors: await prisma.user.findMany({
        where: {
          posts: {
            some: {
              communityId: id
            }
          }
        },
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          _count: {
            select: {
              posts: {
                where: {
                  communityId: id
                }
              }
            }
          }
        },
        orderBy: {
          posts: {
            _count: 'desc'
          }
        },
        take: 5
      })
    };

    return NextResponse.json({ 
      community: {
        ...community,
        analytics
      }
    });
  } catch (error) {
    console.error('Error fetching community details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch community details' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/communities/[id] - Update community
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Validate request body
    const body = await request.json();
    const validatedData = updateCommunitySchema.parse(body);

    // Check if community exists
    const existingCommunity = await prisma.community.findUnique({
      where: { id },
    });

    if (!existingCommunity) {
      return NextResponse.json(
        { error: 'Community not found' },
        { status: 404 }
      );
    }

    // Check if name is already taken (if name is being updated)
    if (validatedData.name && validatedData.name !== existingCommunity.name) {
      const existingName = await prisma.community.findFirst({
        where: { 
          name: validatedData.name,
          id: { not: id }
        },
      });

      if (existingName) {
        return NextResponse.json(
          { error: 'Community name already exists' },
          { status: 400 }
        );
      }
    }

    // Update community
    const updatedCommunity = await prisma.community.update({
      where: { id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            image: true,
          },
        },
        _count: {
          select: {
            posts: true,
            members: true,
          },
        },
      },
    });

    return NextResponse.json({ 
      community: updatedCommunity,
      message: 'Community updated successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error updating community:', error);
    return NextResponse.json(
      { error: 'Failed to update community' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/communities/[id] - Delete community
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check if community exists
    const existingCommunity = await prisma.community.findUnique({
      where: { id },
      select: { id: true, name: true, _count: { select: { posts: true, members: true } } },
    });

    if (!existingCommunity) {
      return NextResponse.json(
        { error: 'Community not found' },
        { status: 404 }
      );
    }

    // Warning for communities with content
    if (existingCommunity._count.posts > 0 || existingCommunity._count.members > 0) {
      const { searchParams } = new URL(request.url);
      const force = searchParams.get('force') === 'true';
      
      if (!force) {
        return NextResponse.json({
          error: 'Community has posts or members',
          details: {
            posts: existingCommunity._count.posts,
            members: existingCommunity._count.members,
            message: 'Add ?force=true to delete anyway'
          }
        }, { status: 400 });
      }
    }

    // Delete community (cascade will handle related records)
    await prisma.community.delete({
      where: { id },
    });

    return NextResponse.json({ 
      message: 'Community deleted successfully',
      deletedCommunityId: id,
      deletedCommunityName: existingCommunity.name
    });
  } catch (error) {
    console.error('Error deleting community:', error);
    return NextResponse.json(
      { error: 'Failed to delete community' },
      { status: 500 }
    );
  }
}
