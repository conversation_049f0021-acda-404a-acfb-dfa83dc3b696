import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for creating/updating settings
const settingSchema = z.object({
  key: z.string().min(1).max(100),
  value: z.string(),
  type: z.enum(['STRING', 'NUMBER', 'BOOLEAN', 'JSON']),
  category: z.string().min(1).max(50),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().default(false),
});

// Validation schema for bulk settings update
const bulkSettingsSchema = z.object({
  settings: z.array(z.object({
    key: z.string(),
    value: z.string(),
  })).min(1),
});

// Default forum settings
const defaultSettings = [
  {
    key: 'forum_name',
    value: 'COMFOR',
    type: 'STRING' as const,
    category: 'general',
    description: 'The name of the forum',
    isPublic: true,
  },
  {
    key: 'forum_description',
    value: 'Communities Forum - Connect, Share, Discuss',
    type: 'STRING' as const,
    category: 'general',
    description: 'Brief description of the forum',
    isPublic: true,
  },
  {
    key: 'allow_registration',
    value: 'true',
    type: 'BOOLEAN' as const,
    category: 'authentication',
    description: 'Allow new user registration',
    isPublic: false,
  },
  {
    key: 'require_email_verification',
    value: 'false',
    type: 'BOOLEAN' as const,
    category: 'authentication',
    description: 'Require email verification for new accounts',
    isPublic: false,
  },
  {
    key: 'max_post_length',
    value: '10000',
    type: 'NUMBER' as const,
    category: 'content',
    description: 'Maximum length for post content',
    isPublic: false,
  },
  {
    key: 'max_comment_length',
    value: '2000',
    type: 'NUMBER' as const,
    category: 'content',
    description: 'Maximum length for comment content',
    isPublic: false,
  },
  {
    key: 'allow_image_uploads',
    value: 'true',
    type: 'BOOLEAN' as const,
    category: 'content',
    description: 'Allow users to upload images',
    isPublic: false,
  },
  {
    key: 'posts_per_page',
    value: '10',
    type: 'NUMBER' as const,
    category: 'display',
    description: 'Number of posts to display per page',
    isPublic: false,
  },
  {
    key: 'maintenance_mode',
    value: 'false',
    type: 'BOOLEAN' as const,
    category: 'system',
    description: 'Enable maintenance mode',
    isPublic: false,
  },
];

// GET /api/admin/settings - Get all settings (admin) or public settings (public)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const publicOnly = searchParams.get('public') === 'true';

    // Check if user is admin for private settings
    let isAdmin = false;
    if (session?.user) {
      const currentUser = await prisma.user.findUnique({
        where: { email: session.user.email! },
      });
      isAdmin = currentUser?.role === 'ADMIN';
    }

    // Build where clause
    const where: any = {};
    
    if (category) {
      where.category = category;
    }
    
    if (publicOnly || !isAdmin) {
      where.isPublic = true;
    }

    // Get settings
    const settings = await prisma.forumSettings.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { key: 'asc' },
      ],
    });

    // Initialize default settings if none exist and user is admin
    if (settings.length === 0 && isAdmin) {
      await prisma.forumSettings.createMany({
        data: defaultSettings,
        skipDuplicates: true,
      });

      // Fetch the newly created settings
      const newSettings = await prisma.forumSettings.findMany({
        where,
        orderBy: [
          { category: 'asc' },
          { key: 'asc' },
        ],
      });

      return NextResponse.json({ settings: newSettings });
    }

    // Group settings by category
    const groupedSettings = settings.reduce((acc, setting) => {
      if (!acc[setting.category]) {
        acc[setting.category] = [];
      }
      acc[setting.category].push(setting);
      return acc;
    }, {} as Record<string, typeof settings>);

    return NextResponse.json({ 
      settings,
      groupedSettings,
      categories: Object.keys(groupedSettings),
    });
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// POST /api/admin/settings - Create new setting (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = settingSchema.parse(body);

    // Check if setting already exists
    const existingSetting = await prisma.forumSettings.findUnique({
      where: { key: validatedData.key },
    });

    if (existingSetting) {
      return NextResponse.json(
        { error: 'Setting with this key already exists' },
        { status: 400 }
      );
    }

    // Create setting
    const setting = await prisma.forumSettings.create({
      data: validatedData,
    });

    return NextResponse.json({
      setting,
      message: 'Setting created successfully',
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error creating setting:', error);
    return NextResponse.json(
      { error: 'Failed to create setting' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/settings - Bulk update settings (admin only)
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { settings } = bulkSettingsSchema.parse(body);

    // Update settings in transaction
    const updatedSettings = await prisma.$transaction(
      settings.map(({ key, value }) =>
        prisma.forumSettings.update({
          where: { key },
          data: { 
            value,
            updatedAt: new Date(),
          },
        })
      )
    );

    return NextResponse.json({
      settings: updatedSettings,
      message: `Updated ${updatedSettings.length} settings`,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
}
