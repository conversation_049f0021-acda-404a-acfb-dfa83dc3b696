import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for updating a setting
const updateSettingSchema = z.object({
  value: z.string(),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().optional(),
});

// GET /api/admin/settings/[key] - Get specific setting
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const { key } = await params;
    const session = await getServerSession(authOptions);

    // Check if user is admin for private settings
    let isAdmin = false;
    if (session?.user) {
      const currentUser = await prisma.user.findUnique({
        where: { email: session.user.email! },
      });
      isAdmin = currentUser?.role === 'ADMIN';
    }

    // Get setting
    const setting = await prisma.forumSettings.findUnique({
      where: { key },
    });

    if (!setting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      );
    }

    // Check if user can access this setting
    if (!setting.isPublic && !isAdmin) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json({ setting });
  } catch (error) {
    console.error('Error fetching setting:', error);
    return NextResponse.json(
      { error: 'Failed to fetch setting' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/settings/[key] - Update specific setting (admin only)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const { key } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Validate request body
    const body = await request.json();
    const validatedData = updateSettingSchema.parse(body);

    // Check if setting exists
    const existingSetting = await prisma.forumSettings.findUnique({
      where: { key },
    });

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      );
    }

    // Update setting
    const updatedSetting = await prisma.forumSettings.update({
      where: { key },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({ 
      setting: updatedSetting,
      message: 'Setting updated successfully'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error updating setting:', error);
    return NextResponse.json(
      { error: 'Failed to update setting' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/settings/[key] - Delete specific setting (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const { key } = await params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Check if setting exists
    const existingSetting = await prisma.forumSettings.findUnique({
      where: { key },
      select: { key: true, description: true },
    });

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      );
    }

    // Prevent deletion of critical settings
    const criticalSettings = [
      'forum_name',
      'allow_registration',
      'maintenance_mode',
    ];

    if (criticalSettings.includes(key)) {
      return NextResponse.json(
        { error: 'Cannot delete critical system setting' },
        { status: 400 }
      );
    }

    // Delete setting
    await prisma.forumSettings.delete({
      where: { key },
    });

    return NextResponse.json({ 
      message: 'Setting deleted successfully',
      deletedKey: key
    });
  } catch (error) {
    console.error('Error deleting setting:', error);
    return NextResponse.json(
      { error: 'Failed to delete setting' },
      { status: 500 }
    );
  }
}
