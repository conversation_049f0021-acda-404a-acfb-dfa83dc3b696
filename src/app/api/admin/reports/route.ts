import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for creating reports
const createReportSchema = z.object({
  type: z.enum(['POST', 'COMMENT', 'USER']),
  reason: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  postId: z.string().optional(),
  commentId: z.string().optional(),
  userId: z.string().optional(),
}).refine((data) => {
  // Ensure exactly one target is specified based on type
  if (data.type === 'POST') return !!data.postId;
  if (data.type === 'COMMENT') return !!data.commentId;
  if (data.type === 'USER') return !!data.userId;
  return false;
}, {
  message: "Must specify the correct target ID for the report type",
});

// Validation schema for updating report status
const updateReportSchema = z.object({
  status: z.enum(['PENDING', 'REVIEWED', 'RESOLVED', 'DISMISSED']),
});

// GET /api/admin/reports - Get all reports (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (type) {
      where.type = type;
    }

    // Get reports with detailed information
    const [reports, total] = await Promise.all([
      prisma.report.findMany({
        where,
        include: {
          reporter: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              image: true,
            },
          },
          post: {
            select: {
              id: true,
              title: true,
              content: true,
              author: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          comment: {
            select: {
              id: true,
              content: true,
              author: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              post: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              image: true,
            },
          },
          handledBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder as 'asc' | 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.report.count({ where }),
    ]);

    // Get statistics
    const stats = {
      totalReports: total,
      pendingReports: await prisma.report.count({ where: { status: 'PENDING' } }),
      reviewedReports: await prisma.report.count({ where: { status: 'REVIEWED' } }),
      resolvedReports: await prisma.report.count({ where: { status: 'RESOLVED' } }),
      dismissedReports: await prisma.report.count({ where: { status: 'DISMISSED' } }),
      reportsToday: await prisma.report.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
    };

    return NextResponse.json({
      reports,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats,
    });
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    );
  }
}

// POST /api/admin/reports - Create a new report (any authenticated user)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Find the user in the database
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const validatedData = createReportSchema.parse(body);

    // Verify the target exists
    let targetExists = false;
    if (validatedData.type === 'POST' && validatedData.postId) {
      const post = await prisma.post.findUnique({ where: { id: validatedData.postId } });
      targetExists = !!post;
    } else if (validatedData.type === 'COMMENT' && validatedData.commentId) {
      const comment = await prisma.comment.findUnique({ where: { id: validatedData.commentId } });
      targetExists = !!comment;
    } else if (validatedData.type === 'USER' && validatedData.userId) {
      const user = await prisma.user.findUnique({ where: { id: validatedData.userId } });
      targetExists = !!user;
    }

    if (!targetExists) {
      return NextResponse.json(
        { error: 'Reported content not found' },
        { status: 404 }
      );
    }

    // Check if user has already reported this content
    const existingReport = await prisma.report.findFirst({
      where: {
        reporterId: currentUser.id,
        type: validatedData.type,
        ...(validatedData.postId && { postId: validatedData.postId }),
        ...(validatedData.commentId && { commentId: validatedData.commentId }),
        ...(validatedData.userId && { userId: validatedData.userId }),
      },
    });

    if (existingReport) {
      return NextResponse.json(
        { error: 'You have already reported this content' },
        { status: 400 }
      );
    }

    // Create the report
    const report = await prisma.report.create({
      data: {
        type: validatedData.type,
        reason: validatedData.reason,
        description: validatedData.description,
        reporterId: currentUser.id,
        postId: validatedData.postId,
        commentId: validatedData.commentId,
        userId: validatedData.userId,
      },
      include: {
        reporter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      report,
      message: 'Report submitted successfully',
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error creating report:', error);
    return NextResponse.json(
      { error: 'Failed to create report' },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/reports - Bulk update report status (admin only)
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { reportIds, status } = z.object({
      reportIds: z.array(z.string()).min(1),
      status: z.enum(['PENDING', 'REVIEWED', 'RESOLVED', 'DISMISSED']),
    }).parse(body);

    // Update reports
    const result = await prisma.report.updateMany({
      where: { id: { in: reportIds } },
      data: {
        status,
        handledById: currentUser.id,
        handledAt: new Date(),
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      message: `Updated ${result.count} reports to ${status}`,
      updatedCount: result.count,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error updating reports:', error);
    return NextResponse.json(
      { error: 'Failed to update reports' },
      { status: 500 }
    );
  }
}
