import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for post updates
const updatePostSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  content: z.string().min(1).optional(),
  published: z.boolean().optional(),
  featured: z.boolean().optional(),
  pinned: z.boolean().optional(),
  locked: z.boolean().optional(),
  communityId: z.string().optional(),
});

// Validation schema for bulk actions
const bulkActionSchema = z.object({
  action: z.enum(['publish', 'unpublish', 'delete', 'feature', 'unfeature', 'pin', 'unpin', 'lock', 'unlock']),
  postIds: z.array(z.string()).min(1),
});

// GET /api/admin/posts - Get all posts with admin details
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const status = searchParams.get('status'); // published, draft, all
    const author = searchParams.get('author');
    const community = searchParams.get('community');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status === 'published') {
      where.published = true;
    } else if (status === 'draft') {
      where.published = false;
    }

    if (author) {
      where.author = {
        name: { contains: author, mode: 'insensitive' }
      };
    }

    if (community) {
      where.community = {
        name: { contains: community, mode: 'insensitive' }
      };
    }

    // Get posts with detailed information
    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              image: true,
            },
          },
          community: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              comments: true,
              likes: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder as 'asc' | 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.post.count({ where }),
    ]);

    // Get additional statistics
    const stats = {
      totalPosts: total,
      publishedPosts: await prisma.post.count({ where: { published: true } }),
      draftPosts: await prisma.post.count({ where: { published: false } }),
      postsToday: await prisma.post.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
    };

    return NextResponse.json({
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats,
    });
  } catch (error) {
    console.error('Error fetching admin posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch posts' },
      { status: 500 }
    );
  }
}

// POST /api/admin/posts - Bulk actions on posts
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, postIds } = bulkActionSchema.parse(body);

    // Verify all posts exist
    const posts = await prisma.post.findMany({
      where: { id: { in: postIds } },
      select: { id: true, title: true },
    });

    if (posts.length !== postIds.length) {
      return NextResponse.json(
        { error: 'Some posts not found' },
        { status: 404 }
      );
    }

    let updateData: any = {};
    let successMessage = '';

    switch (action) {
      case 'publish':
        updateData = { published: true };
        successMessage = `Published ${postIds.length} posts`;
        break;
      case 'unpublish':
        updateData = { published: false };
        successMessage = `Unpublished ${postIds.length} posts`;
        break;
      case 'feature':
        updateData = { featured: true };
        successMessage = `Featured ${postIds.length} posts`;
        break;
      case 'unfeature':
        updateData = { featured: false };
        successMessage = `Unfeatured ${postIds.length} posts`;
        break;
      case 'pin':
        updateData = { pinned: true };
        successMessage = `Pinned ${postIds.length} posts`;
        break;
      case 'unpin':
        updateData = { pinned: false };
        successMessage = `Unpinned ${postIds.length} posts`;
        break;
      case 'lock':
        updateData = { locked: true };
        successMessage = `Locked ${postIds.length} posts`;
        break;
      case 'unlock':
        updateData = { locked: false };
        successMessage = `Unlocked ${postIds.length} posts`;
        break;
      case 'delete':
        await prisma.post.deleteMany({
          where: { id: { in: postIds } },
        });
        return NextResponse.json({
          message: `Deleted ${postIds.length} posts`,
          deletedIds: postIds,
        });
    }

    // Update posts
    const result = await prisma.post.updateMany({
      where: { id: { in: postIds } },
      data: updateData,
    });

    return NextResponse.json({
      message: successMessage,
      updatedCount: result.count,
      updatedIds: postIds,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error performing bulk action:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk action' },
      { status: 500 }
    );
  }
}
