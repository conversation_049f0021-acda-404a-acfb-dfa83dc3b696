import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/admin/analytics - Get advanced analytics data
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const periodDays = parseInt(period);

    // Calculate date ranges
    const now = new Date();
    const startDate = new Date(now.getTime() - periodDays * 24 * 60 * 60 * 1000);
    const previousStartDate = new Date(startDate.getTime() - periodDays * 24 * 60 * 60 * 1000);

    // Get user growth data
    const userGrowthData = await Promise.all(
      Array.from({ length: periodDays }, (_, i) => {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
        const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
        
        return prisma.user.count({
          where: {
            createdAt: {
              gte: date,
              lt: nextDate,
            },
          },
        }).then(count => ({
          date: date.toISOString().split('T')[0],
          users: count,
        }));
      })
    );

    // Get post activity data
    const postActivityData = await Promise.all(
      Array.from({ length: periodDays }, (_, i) => {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
        const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
        
        return Promise.all([
          prisma.post.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          }),
          prisma.comment.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          }),
          prisma.like.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate,
              },
            },
          }),
        ]).then(([posts, comments, likes]) => ({
          date: date.toISOString().split('T')[0],
          posts,
          comments,
          likes,
        }));
      })
    );

    // Get engagement metrics
    const [
      totalUsers,
      activeUsers,
      totalPosts,
      totalComments,
      totalLikes,
      totalCommunities,
      previousPeriodUsers,
      previousPeriodPosts,
      previousPeriodComments,
      topUsers,
      topCommunities,
      engagementByDay,
    ] = await Promise.all([
      // Current period totals
      prisma.user.count(),
      prisma.user.count({
        where: {
          OR: [
            { posts: { some: { createdAt: { gte: startDate } } } },
            { comments: { some: { createdAt: { gte: startDate } } } },
            { likes: { some: { createdAt: { gte: startDate } } } },
          ],
        },
      }),
      prisma.post.count({ where: { createdAt: { gte: startDate } } }),
      prisma.comment.count({ where: { createdAt: { gte: startDate } } }),
      prisma.like.count({ where: { createdAt: { gte: startDate } } }),
      prisma.community.count(),

      // Previous period for comparison
      prisma.user.count({
        where: {
          createdAt: {
            gte: previousStartDate,
            lt: startDate,
          },
        },
      }),
      prisma.post.count({
        where: {
          createdAt: {
            gte: previousStartDate,
            lt: startDate,
          },
        },
      }),
      prisma.comment.count({
        where: {
          createdAt: {
            gte: previousStartDate,
            lt: startDate,
          },
        },
      }),

      // Top users by activity
      prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          image: true,
          _count: {
            select: {
              posts: true,
              comments: true,
              likes: true,
            },
          },
        },
        orderBy: [
          { posts: { _count: 'desc' } },
          { comments: { _count: 'desc' } },
        ],
        take: 10,
      }),

      // Top communities by activity
      prisma.community.findMany({
        select: {
          id: true,
          name: true,
          slug: true,
          description: true,
          _count: {
            select: {
              posts: true,
              members: true,
            },
          },
        },
        orderBy: {
          posts: { _count: 'desc' },
        },
        take: 10,
      }),

      // Daily engagement for the period
      Promise.all(
        Array.from({ length: 7 }, (_, i) => {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
          const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
          
          return Promise.all([
            prisma.post.count({
              where: {
                createdAt: {
                  gte: date,
                  lt: nextDate,
                },
              },
            }),
            prisma.comment.count({
              where: {
                createdAt: {
                  gte: date,
                  lt: nextDate,
                },
              },
            }),
            prisma.like.count({
              where: {
                createdAt: {
                  gte: date,
                  lt: nextDate,
                },
              },
            }),
          ]).then(([posts, comments, likes]) => ({
            date: date.toISOString().split('T')[0],
            engagement: posts + comments + likes,
            posts,
            comments,
            likes,
          }));
        })
      ).then(data => data.reverse()),
    ]);

    // Calculate growth percentages
    const userGrowth = previousPeriodUsers > 0 
      ? ((totalUsers - previousPeriodUsers) / previousPeriodUsers) * 100 
      : 0;
    
    const postGrowth = previousPeriodPosts > 0 
      ? ((totalPosts - previousPeriodPosts) / previousPeriodPosts) * 100 
      : 0;
    
    const commentGrowth = previousPeriodComments > 0 
      ? ((totalComments - previousPeriodComments) / previousPeriodComments) * 100 
      : 0;

    // Calculate engagement rate
    const engagementRate = totalUsers > 0 
      ? (activeUsers / totalUsers) * 100 
      : 0;

    const analytics = {
      overview: {
        totalUsers,
        activeUsers,
        totalPosts,
        totalComments,
        totalLikes,
        totalCommunities,
        engagementRate: Math.round(engagementRate * 100) / 100,
      },
      growth: {
        userGrowth: Math.round(userGrowth * 100) / 100,
        postGrowth: Math.round(postGrowth * 100) / 100,
        commentGrowth: Math.round(commentGrowth * 100) / 100,
      },
      charts: {
        userGrowth: userGrowthData,
        postActivity: postActivityData,
        dailyEngagement: engagementByDay,
      },
      topUsers: topUsers.map(user => ({
        ...user,
        totalActivity: user._count.posts + user._count.comments + user._count.likes,
      })),
      topCommunities: topCommunities.map(community => ({
        ...community,
        activity: community._count.posts,
      })),
      period: {
        days: periodDays,
        startDate: startDate.toISOString(),
        endDate: now.toISOString(),
      },
    };

    return NextResponse.json({ analytics });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}
