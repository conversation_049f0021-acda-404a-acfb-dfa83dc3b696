import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for updating a comment/reply
const updateCommentSchema = z.object({
  content: z.string().min(1).max(10_000).optional(),
});

// Helper: ensure admin
async function requireAdmin() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.email) {
    return { error: 'Authentication required', status: 401 as const };
  }
  const currentUser = await prisma.user.findUnique({ where: { email: session.user.email } });
  if (!currentUser || currentUser.role !== 'ADMIN') {
    return { error: 'Admin access required', status: 403 as const };
  }
  return { currentUser };
}

// GET /api/admin/comments/[id] - fetch single comment with replies (for moderation)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await requireAdmin();
    if ('error' in auth) return NextResponse.json({ error: auth.error }, { status: auth.status });
    const { id } = await params;

    const comment = await prisma.comment.findUnique({
      where: { id },
      include: {
        author: { select: { id: true, name: true, email: true, role: true, image: true } },
        post: { select: { id: true, title: true } },
        replies: {
          include: {
            author: { select: { id: true, name: true, email: true, role: true, image: true } },
            _count: { select: { likes: true, replies: true } },
          },
          orderBy: { createdAt: 'asc' },
        },
        _count: { select: { likes: true, replies: true } },
      },
    });
    if (!comment) return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    return NextResponse.json({ comment });
  } catch (err) {
    console.error('Admin GET comment failed', err);
    return NextResponse.json({ error: 'Failed to fetch comment' }, { status: 500 });
  }
}

// PATCH /api/admin/comments/[id] - edit comment/reply content
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await requireAdmin();
    if ('error' in auth) return NextResponse.json({ error: auth.error }, { status: auth.status });
    const { id } = await params;
    const body = await request.json();
    const data = updateCommentSchema.parse(body);
    if (!Object.keys(data).length) {
      return NextResponse.json({ error: 'No changes provided' }, { status: 400 });
    }
    const existing = await prisma.comment.findUnique({ where: { id }, select: { id: true } });
    if (!existing) return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    const updated = await prisma.comment.update({
      where: { id },
      data: { ...data, updatedAt: new Date() },
      select: { id: true, content: true, updatedAt: true },
    });
    return NextResponse.json({ comment: updated, message: 'Comment updated' });
  } catch (err) {
    if (err instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: err.issues }, { status: 400 });
    }
    console.error('Admin PATCH comment failed', err);
    return NextResponse.json({ error: 'Failed to update comment' }, { status: 500 });
  }
}

// DELETE /api/admin/comments/[id] - delete comment or reply (must have no replies if top-level)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await requireAdmin();
    if ('error' in auth) return NextResponse.json({ error: auth.error }, { status: auth.status });
    const { id } = await params;
    const existing = await prisma.comment.findUnique({
      where: { id },
      select: { id: true, parentId: true, _count: { select: { replies: true } } },
    });
    if (!existing) return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    if (!existing.parentId && existing._count.replies > 0) {
      return NextResponse.json({ error: 'Delete replies first' }, { status: 400 });
    }
    await prisma.comment.delete({ where: { id } });
    return NextResponse.json({ message: 'Comment deleted', deletedId: id });
  } catch (err) {
    console.error('Admin DELETE comment failed', err);
    return NextResponse.json({ error: 'Failed to delete comment' }, { status: 500 });
  }
}
