import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const bulkActionSchema = z.object({
  action: z.enum(['delete']),
  commentIds: z.array(z.string()).min(1),
});

async function requireAdmin() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.email) {
    return { error: 'Authentication required', status: 401 as const };
  }
  const currentUser = await prisma.user.findUnique({ where: { email: session.user.email } });
  if (!currentUser || currentUser.role !== 'ADMIN') {
    return { error: 'Admin access required', status: 403 as const };
  }
  return { currentUser };
}

// GET /api/admin/comments - paginated comments for moderation
export async function GET(request: NextRequest) {
  try {
    const auth = await requireAdmin();
    if ('error' in auth) return NextResponse.json({ error: auth.error }, { status: auth.status });
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(100, parseInt(searchParams.get('limit') || '20'));
    const search = searchParams.get('search');
    const postId = searchParams.get('postId');
    const parentOnly = searchParams.get('parentOnly') === 'true';
    const skip = (page - 1) * limit;

    const where: any = {};
    if (search) {
      where.content = { contains: search, mode: 'insensitive' };
    }
    if (postId) where.postId = postId;
    if (parentOnly) where.parentId = null;

    const [comments, total] = await Promise.all([
      prisma.comment.findMany({
        where,
        include: {
          author: { select: { id: true, name: true, email: true, role: true, image: true } },
          post: { select: { id: true, title: true } },
          _count: { select: { replies: true, likes: true } },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.comment.count({ where }),
    ]);

    return NextResponse.json({
      comments,
      pagination: { page, limit, total, pages: Math.ceil(total / limit) },
    });
  } catch (err) {
    console.error('Admin list comments failed', err);
    return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 });
  }
}

// POST /api/admin/comments - bulk actions (currently only delete)
export async function POST(request: NextRequest) {
  try {
    const auth = await requireAdmin();
    if ('error' in auth) return NextResponse.json({ error: auth.error }, { status: auth.status });
    const body = await request.json();
    const { action, commentIds } = bulkActionSchema.parse(body);

    // verify existence
    const existing = await prisma.comment.findMany({ where: { id: { in: commentIds } }, select: { id: true } });
    if (existing.length !== commentIds.length) {
      return NextResponse.json({ error: 'Some comments not found' }, { status: 404 });
    }

    switch (action) {
      case 'delete': {
        // For safety: ensure no top-level comment with remaining replies
        const parents = await prisma.comment.findMany({
          where: { id: { in: commentIds }, parentId: null },
          select: { id: true, _count: { select: { replies: true } } },
        });
        const blocked = parents.filter(p => p._count.replies > 0).map(p => p.id);
        if (blocked.length) {
          return NextResponse.json({ error: 'Some top-level comments still have replies', blocked }, { status: 400 });
        }
        const result = await prisma.comment.deleteMany({ where: { id: { in: commentIds } } });
        return NextResponse.json({ message: `Deleted ${result.count} comments`, deletedIds: commentIds });
      }
    }
  } catch (err) {
    if (err instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: err.issues }, { status: 400 });
    }
    console.error('Admin bulk comment action failed', err);
    return NextResponse.json({ error: 'Failed to perform action' }, { status: 500 });
  }
}
