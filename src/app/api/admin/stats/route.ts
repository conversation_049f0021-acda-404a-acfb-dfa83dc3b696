import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/admin/stats - Get admin dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Find the user in the database to check role
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get current date for "today" calculations
    const today = new Date();
    const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // Fetch all statistics in parallel
    const [
      totalUsers,
      totalPosts,
      totalComments,
      totalLikes,
      totalCommunities,
      newUsersToday,
      newPostsToday,
      newCommentsToday,
      recentPosts,
      usersByRole,
      postsThisWeek,
      commentsThisWeek
    ] = await Promise.all([
      // Total counts
      prisma.user.count(),
      prisma.post.count(),
      prisma.comment.count(),
      prisma.like.count(),
      prisma.community.count(),
      
      // Today's new content
      prisma.user.count({
        where: {
          createdAt: {
            gte: startOfToday
          }
        }
      }),
      prisma.post.count({
        where: {
          createdAt: {
            gte: startOfToday
          }
        }
      }),
      prisma.comment.count({
        where: {
          createdAt: {
            gte: startOfToday
          }
        }
      }),
      
      // Recent posts for activity feed
      prisma.post.findMany({
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              role: true
            }
          },
          _count: {
            select: {
              comments: true,
              likes: true
            }
          }
        }
      }),
      
      // User distribution by role
      prisma.user.groupBy({
        by: ['role'],
        _count: {
          role: true
        }
      }),
      
      // Posts this week
      prisma.post.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // Comments this week
      prisma.comment.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      })
    ]);

    // Format user role distribution
    const roleDistribution = usersByRole.reduce((acc, item) => {
      acc[item.role] = item._count.role;
      return acc;
    }, {} as Record<string, number>);

    // Format recent posts
    const formattedRecentPosts = recentPosts.map(post => ({
      id: post.id,
      title: post.title,
      author: post.author.name || 'Unknown',
      authorRole: post.author.role,
      createdAt: post.createdAt,
      published: post.published,
      likes: post._count.likes,
      comments: post._count.comments
    }));

    const stats = {
      // Main statistics
      totalUsers,
      totalPosts,
      totalComments,
      totalLikes,
      totalCommunities,
      
      // Today's activity
      newUsersToday,
      newPostsToday,
      newCommentsToday,
      
      // Weekly activity
      postsThisWeek,
      commentsThisWeek,
      
      // Additional metrics
      roleDistribution,
      recentPosts: formattedRecentPosts,
      
      // Calculated metrics
      avgPostsPerUser: totalUsers > 0 ? Math.round((totalPosts / totalUsers) * 100) / 100 : 0,
      avgCommentsPerPost: totalPosts > 0 ? Math.round((totalComments / totalPosts) * 100) / 100 : 0,
      engagementRate: totalPosts > 0 ? Math.round(((totalLikes + totalComments) / totalPosts) * 100) / 100 : 0
    };

    return NextResponse.json({ stats });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch statistics' },
      { status: 500 }
    );
  }
}
