import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import bcrypt from 'bcryptjs';

// Validation schema for bulk user actions
const bulkActionSchema = z.object({
  action: z.enum(['suspend', 'unsuspend', 'delete', 'changeRole', 'create']),
  userIds: z.array(z.string()).min(1).optional(),
  newRole: z.enum(['USER', 'EDITOR', 'ADMIN']).optional(),
  userData: z.object({
    name: z.string().min(1).max(100),
    email: z.string().email(),
    password: z.string().min(6),
    role: z.enum(['USER', 'EDITOR', 'ADMIN']).optional().default('USER'),
    bio: z.string().max(500).optional(),
  }).optional(),
});

// GET /api/admin/users - Get all users with admin details
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (role && role !== 'all') {
      where.role = role;
    }

    // Get users with detailed information
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          image: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              posts: true,
              comments: true,
              likes: true,
              communities: true,
              communityMemberships: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder as 'asc' | 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    // Get additional statistics
    const stats = {
      totalUsers: total,
      adminUsers: await prisma.user.count({ where: { role: 'ADMIN' } }),
      editorUsers: await prisma.user.count({ where: { role: 'EDITOR' } }),
      regularUsers: await prisma.user.count({ where: { role: 'USER' } }),
      usersToday: await prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
    };

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats,
    });
  } catch (error) {
    console.error('Error fetching admin users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Bulk actions on users
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if current user is admin
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, userIds, newRole, userData } = bulkActionSchema.parse(body);

    // Handle create user action separately
    if (action === 'create') {
      if (!userData) {
        return NextResponse.json(
          { error: 'User data is required for create action' },
          { status: 400 }
        );
      }

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email },
      });

      if (existingUser) {
        return NextResponse.json(
          { error: 'A user with this email already exists' },
          { status: 409 }
        );
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12);

      // Create user
      const newUser = await prisma.user.create({
        data: {
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
          role: userData.role || 'USER',
          bio: userData.bio || null,
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
        },
      });

      return NextResponse.json({
        message: 'User created successfully',
        user: newUser,
      });
    }

    // For other actions, verify users exist
    if (!userIds || userIds.length === 0) {
      return NextResponse.json(
        { error: 'User IDs are required for this action' },
        { status: 400 }
      );
    }

    const users = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: { id: true, name: true, email: true, role: true },
    });

    if (users.length !== userIds.length) {
      return NextResponse.json(
        { error: 'Some users not found' },
        { status: 404 }
      );
    }

    // Prevent admin from modifying their own account in bulk operations
    const currentUserInList = users.find(u => u.email === currentUser.email);
    if (currentUserInList && (action === 'delete' || action === 'suspend')) {
      return NextResponse.json(
        { error: 'Cannot perform this action on your own account' },
        { status: 400 }
      );
    }

    let updateData: any = {};
    let successMessage = '';

    switch (action) {
      case 'suspend':
        // Note: This would require adding a 'suspended' field to the User model
        updateData = { suspended: true };
        successMessage = `Suspended ${userIds.length} users`;
        break;
      case 'unsuspend':
        updateData = { suspended: false };
        successMessage = `Unsuspended ${userIds.length} users`;
        break;
      case 'changeRole':
        if (!newRole) {
          return NextResponse.json(
            { error: 'New role is required for role change action' },
            { status: 400 }
          );
        }
        updateData = { role: newRole };
        successMessage = `Changed role to ${newRole} for ${userIds.length} users`;
        break;
      case 'delete':
        // Prevent deletion of admin users
        const adminUsers = users.filter(u => u.role === 'ADMIN');
        if (adminUsers.length > 0) {
          return NextResponse.json(
            { error: 'Cannot delete admin users' },
            { status: 400 }
          );
        }
        
        await prisma.user.deleteMany({
          where: { id: { in: userIds } },
        });
        return NextResponse.json({
          message: `Deleted ${userIds.length} users`,
          deletedIds: userIds,
        });
    }

    // Update users
    const result = await prisma.user.updateMany({
      where: { id: { in: userIds } },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      message: successMessage,
      updatedCount: result.count,
      updatedIds: userIds,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error performing bulk user action:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk action' },
      { status: 500 }
    );
  }
}
