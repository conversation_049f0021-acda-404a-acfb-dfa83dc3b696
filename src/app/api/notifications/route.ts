import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for creating notifications
const createNotificationSchema = z.object({
  type: z.enum(['LIKE', 'COMMENT', 'FOLLOW', 'MENTION', 'POST', 'COMMUNITY']),
  title: z.string().min(1).max(100),
  message: z.string().min(1).max(500),
  actionUrl: z.string().optional(),
  fromUserId: z.string().optional(),
  postId: z.string().optional(),
  commentId: z.string().optional(),
  userId: z.string(), // Target user who will receive the notification
});

// GET /api/notifications - Get current user's notifications
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const filter = searchParams.get('filter'); // 'unread', 'likes', 'comments', 'follows'
    const skip = (page - 1) * limit;

    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Build where clause
    const where: any = { userId: currentUser.id };
    
    if (filter) {
      switch (filter) {
        case 'unread':
          where.read = false;
          break;
        case 'likes':
          where.type = 'LIKE';
          break;
        case 'comments':
          where.type = { in: ['COMMENT', 'MENTION'] };
          break;
        case 'follows':
          where.type = 'FOLLOW';
          break;
      }
    }

    const [notifications, total, unreadCount] = await Promise.all([
      prisma.notification.findMany({
        where,
        include: {
          fromUser: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          post: {
            select: {
              id: true,
              title: true,
            },
          },
          comment: {
            select: {
              id: true,
              content: true,
              post: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.notification.count({ where }),
      prisma.notification.count({
        where: {
          userId: currentUser.id,
          read: false,
        },
      }),
    ]);

    // If no notifications exist, create a welcome notification for new users
    if (notifications.length === 0 && total === 0) {
      try {
        const welcomeNotification = await prisma.notification.create({
          data: {
            type: 'COMMUNITY',
            title: 'Welcome to the Forum!',
            message: 'Thanks for joining our community! Start by exploring posts, following other users, and joining communities that interest you.',
            actionUrl: '/communities',
            userId: currentUser.id,
          },
          include: {
            fromUser: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
            post: {
              select: {
                id: true,
                title: true,
              },
            },
            comment: {
              select: {
                id: true,
                content: true,
                post: {
                  select: {
                    id: true,
                    title: true,
                  },
                },
              },
            },
          },
        });

        return NextResponse.json({
          notifications: [welcomeNotification],
          pagination: {
            page: 1,
            limit,
            total: 1,
            pages: 1,
          },
          unreadCount: 1,
        });
      } catch (error) {
        console.error('Failed to create welcome notification:', error);
        // Return empty result if welcome notification creation fails
        return NextResponse.json({
          notifications: [],
          pagination: {
            page,
            limit,
            total: 0,
            pages: 0,
          },
          unreadCount: 0,
        });
      }
    }

    return NextResponse.json({
      notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      unreadCount,
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 });
  }
}

// POST /api/notifications - Create a new notification (system/admin use)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user has permission to create notifications
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createNotificationSchema.parse(body);

    const notification = await prisma.notification.create({
      data: {
        ...validatedData,
        userId: body.userId, // Target user
      },
      include: {
        fromUser: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json(notification, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error creating notification:', error);
    return NextResponse.json({ error: 'Failed to create notification' }, { status: 500 });
  }
}

// PATCH /api/notifications - Mark notifications as read
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const body = await request.json();
    const { notificationIds, markAllAsRead } = body;

    if (markAllAsRead) {
      // Mark all user's notifications as read
      await prisma.notification.updateMany({
        where: {
          userId: currentUser.id,
          read: false,
        },
        data: { read: true },
      });
    } else if (notificationIds && Array.isArray(notificationIds)) {
      // Mark specific notifications as read
      await prisma.notification.updateMany({
        where: {
          id: { in: notificationIds },
          userId: currentUser.id,
        },
        data: { read: true },
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating notifications:', error);
    return NextResponse.json({ error: 'Failed to update notifications' }, { status: 500 });
  }
}
