import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/users/count - public endpoint returning total user count
export async function GET() {
  try {
    const userCount = await prisma.user.count();
    return NextResponse.json({ userCount });
  } catch (error) {
    console.error('Error getting user count:', error);
    return NextResponse.json({ userCount: 0 }, { status: 500 });
  }
}
