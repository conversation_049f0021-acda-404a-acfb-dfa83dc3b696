import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/users/public - Get public users (accessible to all)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const isAuthenticated = !!session?.user;
    
    // Get current user to check if they're admin
    const currentUser = session?.user ? await prisma.user.findUnique({
      where: { email: session.user.email! },
    }) : null;
    
    const isAdmin = currentUser?.role === 'ADMIN';

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause - for now, show all users since privacy fields don't exist yet
    const where: any = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { bio: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          name: true,
          image: true,
          bio: true,
          createdAt: true,
          email: true, // We'll filter this based on authentication
          _count: {
            select: {
              posts: true,
              comments: true,
              likes: true,
              followers: true,
              following: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    // Filter user data based on privacy settings
    const publicUsers = users.map(user => {
      const publicUser: any = {
        id: user.id,
        name: user.name,
        image: user.image,
        bio: user.bio,
        createdAt: user.createdAt,
        _count: user._count,
      };

      // For now, only show email to authenticated users (privacy fields don't exist yet)
      if (isAuthenticated) {
        publicUser.email = user.email;
      }

      return publicUser;
    });

    return NextResponse.json({
      users: publicUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching public users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}
