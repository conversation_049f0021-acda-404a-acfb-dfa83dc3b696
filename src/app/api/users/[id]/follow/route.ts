import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

// Helper to resolve params (Next.js 15 may pass params as a Promise in route handlers)
async function resolveParams(context: any): Promise<{ id?: string }> {
  try {
    // If context.params is a Promise (as per new async params API), await it
    const params = await context?.params;
    return params || {};
  } catch {
    return {};
  }
}

async function getCurrentUserId(session: any): Promise<string | undefined> {
  if (!session?.user) return undefined;
  let currentUserId = (session.user as any).id as string | undefined;
  if (!currentUserId && session.user.email) {
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });
    currentUserId = currentUser?.id;
  }
  return currentUserId;
}

// GET /api/users/[id]/follow - Check if current user is following this user
export async function GET(
  _request: NextRequest,
  context: any // Runtime validated
) {
  try {
    const { id: targetUserId } = await resolveParams(context);
    if (!targetUserId) return NextResponse.json({ isFollowing: false });
    const session = await getServerSession(authOptions);
    const currentUserId = await getCurrentUserId(session);
    if (!currentUserId) return NextResponse.json({ isFollowing: false });

    const follow = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: currentUserId,
          followingId: targetUserId
        }
      }
    });
    return NextResponse.json({ isFollowing: !!follow });
  } catch (error) {
    console.error('Error checking follow status:', error);
    return NextResponse.json({ isFollowing: false });
  }
}

// POST /api/users/[id]/follow - Follow this user
export async function POST(
  _request: NextRequest,
  context: any
) {
  try {
    const { id: targetUserId } = await resolveParams(context);
    if (!targetUserId) return NextResponse.json({ error: 'Target user id missing' }, { status: 400 });
    const session = await getServerSession(authOptions);
    if (!session?.user) return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    const currentUserId = await getCurrentUserId(session);
    if (!currentUserId) return NextResponse.json({ error: 'User not found' }, { status: 404 });
    if (currentUserId === targetUserId) return NextResponse.json({ error: 'Cannot follow yourself' }, { status: 400 });

    const targetUserExists = await prisma.user.findUnique({
      where: { id: targetUserId },
      select: { id: true }
    });
    if (!targetUserExists) return NextResponse.json({ error: 'Target user not found' }, { status: 404 });

    const existing = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: currentUserId,
          followingId: targetUserId
        }
      }
    });
    if (existing) return NextResponse.json({ error: 'Already following' }, { status: 400 });

    await prisma.follow.create({ data: { followerId: currentUserId, followingId: targetUserId } });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error following user:', error);
    return NextResponse.json({ error: 'Failed to follow user' }, { status: 500 });
  }
}

// DELETE /api/users/[id]/follow - Unfollow this user
export async function DELETE(
  _request: NextRequest,
  context: any
) {
  try {
    const { id: targetUserId } = await resolveParams(context);
    if (!targetUserId) return NextResponse.json({ error: 'Target user id missing' }, { status: 400 });
    const session = await getServerSession(authOptions);
    if (!session?.user) return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    const currentUserId = await getCurrentUserId(session);
    if (!currentUserId) return NextResponse.json({ error: 'User not found' }, { status: 404 });

    try {
      await prisma.follow.delete({
        where: { followerId_followingId: { followerId: currentUserId, followingId: targetUserId } }
      });
    } catch (error: any) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return NextResponse.json({ error: 'Not following' }, { status: 404 });
      }
      throw error;
    }
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error unfollowing user:', error);
    return NextResponse.json({ error: 'Failed to unfollow user' }, { status: 500 });
  }
}
