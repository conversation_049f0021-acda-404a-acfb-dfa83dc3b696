import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/following - Get users the current user is following
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    const follows = await prisma.follow.findMany({
      where: { followerId: currentUser.id },
      include: {
        following: {
          select: {
            id: true,
            name: true,
            image: true,
            bio: true,
            createdAt: true,
            email: true,
            _count: {
              select: { 
                posts: true,
                followers: true,
                following: true,
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });
    const users = follows.map(f => ({
      ...f.following,
      followedAt: f.createdAt
    }));
    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching following:', error);
    return NextResponse.json({ error: 'Failed to fetch following' }, { status: 500 });
  }
}

// POST /api/following - Follow a user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    const { userId } = await request.json();
    if (!userId || userId === currentUser.id) {
      return NextResponse.json({ error: 'Invalid userId' }, { status: 400 });
    }
    // Check if already following
    const existing = await prisma.follow.findUnique({
      where: { followerId_followingId: { followerId: currentUser.id, followingId: userId } }
    });
    if (existing) {
      return NextResponse.json({ error: 'Already following' }, { status: 400 });
    }
    await prisma.follow.create({
      data: { followerId: currentUser.id, followingId: userId }
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error following user:', error);
    return NextResponse.json({ error: 'Failed to follow user' }, { status: 500 });
  }
}

// DELETE /api/following - Unfollow a user
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    const { userId } = await request.json();
    if (!userId || userId === currentUser.id) {
      return NextResponse.json({ error: 'Invalid userId' }, { status: 400 });
    }
    await prisma.follow.delete({
      where: { followerId_followingId: { followerId: currentUser.id, followingId: userId } }
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error unfollowing user:', error);
    return NextResponse.json({ error: 'Failed to unfollow user' }, { status: 500 });
  }
}
