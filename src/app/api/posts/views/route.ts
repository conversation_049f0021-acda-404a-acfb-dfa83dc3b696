import { NextRequest, NextResponse } from 'next/server';
import { getRedis, viewsKey } from '@/lib/redis';

// POST /api/posts/views - get view counts for many posts
// Body: { ids: string[] }
export async function POST(req: NextRequest) {
  try {
    const { ids } = await req.json() as { ids: string[] };
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({ error: 'ids array required' }, { status: 400 });
    }
    const redis = getRedis();
    if (!redis) {
      return NextResponse.json({ counts: Object.fromEntries(ids.map(id => [id, 0])) });
    }
    const keys = ids.map(viewsKey);
    const vals = await redis.mget(...keys);
    const counts: Record<string, number> = {};
    ids.forEach((id, i) => {
      const v = vals[i];
      counts[id] = v ? parseInt(v, 10) : 0;
    });
    return NextResponse.json({ counts });
  } catch (e) {
    return NextResponse.json({ error: 'invalid body' }, { status: 400 });
  }
}
