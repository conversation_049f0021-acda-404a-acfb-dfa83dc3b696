import { NextRequest, NextResponse } from 'next/server';
import { getRedis, viewsKey } from '@/lib/redis';

// Helper to resolve params (Next.js 15 may pass params as a Promise)
async function resolveParams(context: any): Promise<{ id?: string }> {
  try {
    const params = await context?.params;
    return params || {};
  } catch {
    return {};
  }
}

// GET /api/posts/[id]/views - returns current view count for a post
export async function GET(_req: NextRequest, context: any) {
  const { id } = await resolveParams(context);
  if (!id) return NextResponse.json({ views: 0 });
  const redis = getRedis();
  if (!redis) return NextResponse.json({ views: 0 }, { status: 200 });
  const key = viewsKey(id);
  const val = await redis.get(key);
  const views = val ? parseInt(val, 10) : 0;
  return NextResponse.json({ views });
}

// POST /api/posts/[id]/views - increment view count (idempotent per request call)
export async function POST(_req: NextRequest, context: any) {
  const { id } = await resolveParams(context);
  if (!id) return NextResponse.json({ views: 0 }, { status: 400 });
  const redis = getRedis();
  if (!redis) return NextResponse.json({ views: 0 }, { status: 200 });
  const key = viewsKey(id);
  const views = await redis.incr(key);
  return NextResponse.json({ views });
}
