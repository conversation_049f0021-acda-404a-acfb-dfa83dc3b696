import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { emitPostCreated } from '@/lib/realtime';

// Validation schema for creating posts
const createPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  content: z.string().min(1, 'Content is required'),
  published: z.boolean().optional().default(false),
  communityId: z.string().optional().nullable(),
  videoUrl: z.string().optional().nullable().refine(
    (val) => !val || val === '' || z.string().url().safeParse(val).success,
    { message: 'Must be a valid URL or empty' }
  ),
  images: z.array(z.string()).optional().default([]),
});

// GET /api/posts - Get all posts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const published = searchParams.get('published') === 'true';
    const authorId = searchParams.get('authorId');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (published !== undefined) {
      where.published = published;
    }
    if (authorId) {
      where.authorId = authorId;
    }
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
              image: true,
            },
          },
          community: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              comments: true,
              likes: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.post.count({ where }),
    ]);

    return NextResponse.json({
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch posts' },
      { status: 500 }
    );
  }
}

// POST /api/posts - Create a new post
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createPostSchema.parse(body);

    // Find the user in the database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Validate community exists if communityId is provided
    if (validatedData.communityId) {
      const community = await prisma.community.findUnique({
        where: { id: validatedData.communityId },
      });

      if (!community) {
        return NextResponse.json(
          { error: 'Community not found' },
          { status: 404 }
        );
      }

      // For now, allow posting to any community (membership check can be added later)
      // TODO: Implement proper community membership validation
      // const membership = await prisma.communityMember.findUnique({
      //   where: {
      //     userId_communityId: {
      //       userId: user.id,
      //       communityId: validatedData.communityId,
      //     },
      //   },
      // });

      // if (!membership) {
      //   return NextResponse.json(
      //     { error: 'You must be a member of this community to post' },
      //     { status: 403 }
      //   );
      // }
    }

  const post = await prisma.post.create({
      data: {
        title: validatedData.title,
        content: validatedData.content,
        published: validatedData.published,
        authorId: user.id,
        communityId: validatedData.communityId || null,
        videoUrl: validatedData.videoUrl || null,
        images: validatedData.images || [],
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            image: true,
          },
        },
        community: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            comments: true,
            likes: true,
          },
        },
      },
    });

  // Fire-and-forget realtime emit (no error if IO not present)
  emitPostCreated({ id: post.id, title: post.title, authorId: post.author.id });

    return NextResponse.json(post, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error creating post:', error);
    return NextResponse.json(
      { error: 'Failed to create post' },
      { status: 500 }
    );
  }
}
