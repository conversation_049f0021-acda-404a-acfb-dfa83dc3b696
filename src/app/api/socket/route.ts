import { NextRequest } from 'next/server';
import { Server as IOServer, Socket } from 'socket.io';
import { Server as HTTPServer } from 'http';

// We have to hold a reference on the global to prevent re-instantiation in dev hot reload
interface SocketServer extends HTTPServer {
  io?: IOServer;
}

declare const globalThis: {
  _io?: IOServer;
} & typeof global;

export const config = {
  runtime: 'nodejs',
};

export async function GET(req: NextRequest) {
  // We cannot upgrade inside a Route Handler reliably; just signal status.
  return new Response('Socket placeholder (no upgrade) – deploy a custom Node server for real-time websockets.', { status: 200 });
}

// NOTE: Socket.IO server instantiation here does NOT attach to Next.js internal HTTP server.
// To avoid repeated failed client WS attempts, we remove in-route instantiation. For production,
// implement a custom server (e.g. server.js) that mounts both Next handler and Socket.IO.

// No additional exports allowed; helper accessors live in src/lib/realtime.ts
export {};
