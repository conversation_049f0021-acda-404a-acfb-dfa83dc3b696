import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/feed - Get activity feed for authenticated user
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });

    if (!currentUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const type = searchParams.get('type'); // post, comment, like, follow

    const skip = (page - 1) * limit;

    // Get users that the current user is following
    const following = await prisma.follow.findMany({
      where: { followerId: currentUser.id },
      select: { followingId: true }
    });

    const followingIds = following.map(f => f.followingId);

    // If not following anyone, return empty feed
    if (followingIds.length === 0) {
      return NextResponse.json({
        activities: [],
        pagination: {
          page,
          limit,
          total: 0,
          pages: 0,
        },
      });
    }

    const activities = [];

    // Get recent posts from followed users
    if (!type || type === 'post') {
      const posts = await prisma.post.findMany({
        where: {
          authorId: { in: followingIds },
          published: true,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          community: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              comments: true,
              likes: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      activities.push(...posts.map(post => ({
        id: `post-${post.id}`,
        type: 'post',
        user: {
          id: post.author.id,
          name: post.author.name,
          username: post.author.name?.toLowerCase().replace(/\s+/g, '_') || 'user',
          avatar: post.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(post.author.name || 'User')}&background=random`,
        },
        content: post.content.substring(0, 200) + (post.content.length > 200 ? '...' : ''),
        postTitle: post.title,
        postId: post.id,
        timestamp: post.createdAt,
        isRead: true, // For now, mark all as read
        community: post.community,
        _count: post._count,
      })));
    }

    // Get recent comments from followed users
    if (!type || type === 'comment') {
      const comments = await prisma.comment.findMany({
        where: {
          authorId: { in: followingIds },
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          post: {
            select: {
              id: true,
              title: true,
            },
          },
          _count: {
            select: {
              likes: true,
              replies: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      activities.push(...comments.map(comment => ({
        id: `comment-${comment.id}`,
        type: 'comment',
        user: {
          id: comment.author.id,
          name: comment.author.name,
          username: comment.author.name?.toLowerCase().replace(/\s+/g, '_') || 'user',
          avatar: comment.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.author.name || 'User')}&background=random`,
        },
        content: comment.content.substring(0, 200) + (comment.content.length > 200 ? '...' : ''),
        postTitle: comment.post.title,
        postId: comment.post.id,
        timestamp: comment.createdAt,
        isRead: true,
        _count: comment._count,
      })));
    }

    // Get recent likes from followed users
    if (!type || type === 'like') {
      const likes = await prisma.like.findMany({
        where: {
          userId: { in: followingIds },
          post: { isNot: null }, // Only post likes for now
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          post: {
            select: {
              id: true,
              title: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      activities.push(...likes.map(like => ({
        id: `like-${like.id}`,
        type: 'like',
        user: {
          id: like.user.id,
          name: like.user.name,
          username: like.user.name?.toLowerCase().replace(/\s+/g, '_') || 'user',
          avatar: like.user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(like.user.name || 'User')}&background=random`,
        },
        postTitle: like.post?.title,
        postId: like.post?.id,
        timestamp: like.createdAt,
        isRead: true,
      })));
    }

    // Get recent follows from followed users
    if (!type || type === 'follow') {
      const follows = await prisma.follow.findMany({
        where: {
          followerId: { in: followingIds },
        },
        include: {
          follower: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          following: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      activities.push(...follows.map(follow => ({
        id: `follow-${follow.id}`,
        type: 'follow',
        user: {
          id: follow.follower.id,
          name: follow.follower.name,
          username: follow.follower.name?.toLowerCase().replace(/\s+/g, '_') || 'user',
          avatar: follow.follower.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(follow.follower.name || 'User')}&background=random`,
        },
        targetUser: {
          name: follow.following.name,
          username: follow.following.name?.toLowerCase().replace(/\s+/g, '_') || 'user',
        },
        timestamp: follow.createdAt,
        isRead: true,
      })));
    }

    // Sort all activities by timestamp
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Apply pagination
    const paginatedActivities = activities.slice(skip, skip + limit);

    return NextResponse.json({
      activities: paginatedActivities,
      pagination: {
        page,
        limit,
        total: activities.length,
        pages: Math.ceil(activities.length / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching feed:', error);
    return NextResponse.json(
      { error: 'Failed to fetch feed' },
      { status: 500 }
    );
  }
}
