"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import Link from "next/link";
import UserCard from "@/components/UserCard";
import { useFollowing } from "@/hooks/useFollowing";

// Types
interface Follower {
  id: string;
  name: string;
  email?: string;
  image: string | null;
  bio: string | null;
  role: string;
  createdAt: string;
  followedAt: string;
  isFollowing: boolean;
  _count: {
    posts: number;
    followers: number;
    following: number;
  };
}

interface FollowersResponse {
  followers: Follower[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export default function FollowersPage() {
  const { data: session } = useSession();
  const { followingData, toggleFollow } = useFollowing();
  const [followers, setFollowers] = useState<Follower[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"recent" | "name" | "followers">("recent");
  const [filterBy, setFilterBy] = useState<"all" | "following" | "not_following">("all");

  // Fetch followers from API
  useEffect(() => {
    const fetchFollowers = async () => {
      if (!session?.user) return;

      try {
        setLoading(true);
        const searchParam = searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : '';
        const response = await fetch(`/api/followers?limit=100${searchParam}`);
        
        if (response.ok) {
          const data: FollowersResponse = await response.json();
          setFollowers(data.followers);
        } else {
          console.error('Failed to fetch followers');
          setFollowers([]);
        }
      } catch (error) {
        console.error('Error fetching followers:', error);
        setFollowers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFollowers();
  }, [session, searchQuery]);

  // Update followers with current following status
  useEffect(() => {
    if (followers.length > 0) {
      setFollowers(prev => 
        prev.map(follower => {
          const followingStatus = followingData.get(follower.id);
          return {
            ...follower,
            isFollowing: followingStatus?.isFollowing || false,
          };
        })
      );
    }
  }, [followingData, followers.length]);

  const handleFollowChange = async (userId: string, isFollowing: boolean, newCount: number) => {
    await toggleFollow(userId);
    setFollowers(prev =>
      prev.map(follower =>
        follower.id === userId
          ? { ...follower, isFollowing }
          : follower
      )
    );
  };

  const filteredFollowers = followers
    .filter(follower => {
      const matchesSearch = follower.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           (follower.bio && follower.bio.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesFilter = filterBy === "all" || 
                           (filterBy === "following" && follower.isFollowing) ||
                           (filterBy === "not_following" && !follower.isFollowing);
      
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "followers":
          return b._count.followers - a._count.followers;
        case "recent":
        default:
          return new Date(b.followedAt).getTime() - new Date(a.followedAt).getTime();
      }
    });

  const formatFollowDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short',
      day: 'numeric'
    });
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30 flex items-center justify-center">
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body text-center p-12">
            <h2 className="text-2xl font-bold mb-4">Sign In Required</h2>
            <p className="text-base-content/70 mb-6">Please sign in to view your followers.</p>
            <Link href="/auth/signin" className="btn btn-primary rounded-full">
              Sign In
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
      <div className="max-w-7xl mx-auto space-y-8 p-4">
        {/* Header */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body p-8">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-2">
                  Your Followers
                </h1>
                <p className="text-base-content/70 text-lg">
                  People who are following you
                </p>
              </div>
              <div className="stats shadow-lg bg-gradient-to-r from-primary/5 to-accent/5">
                <div className="stat">
                  <div className="stat-title text-xs">Total Followers</div>
                  <div className="stat-value text-2xl text-primary">{followers.length}</div>
                </div>
                <div className="stat">
                  <div className="stat-title text-xs">Following Back</div>
                  <div className="stat-value text-2xl text-accent">
                    {followers.filter(f => f.isFollowing).length}
                  </div>
                </div>
                <div className="stat">
                  <div className="stat-title text-xs">Mutual</div>
                  <div className="stat-value text-2xl text-info">
                    {followers.filter(f => f.isFollowing).length}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
          <div className="card-body p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="form-control">
                  <div className="input-group">
                    <span className="bg-base-200">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </span>
                    <input
                      type="text"
                      placeholder="Search followers..."
                      className="input input-bordered w-full"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Sort */}
              <div className="form-control">
                <select 
                  className="select select-bordered"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                >
                  <option value="recent">Recently Followed</option>
                  <option value="name">Name (A-Z)</option>
                  <option value="followers">Most Followers</option>
                </select>
              </div>

              {/* Filter */}
              <div className="form-control">
                <select 
                  className="select select-bordered"
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as any)}
                >
                  <option value="all">All Followers</option>
                  <option value="following">Following Back</option>
                  <option value="not_following">Not Following Back</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Followers List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredFollowers.length === 0 ? (
            <div className="col-span-full">
              <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
                <div className="card-body text-center py-16">
                  <div className="text-6xl mb-4">👥</div>
                  <h3 className="text-2xl font-bold mb-2">No followers found</h3>
                  <p className="text-base-content/70">
                    {searchQuery ? "Try adjusting your search or filters." : "Start engaging with the community to gain followers!"}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            filteredFollowers.map((follower) => (
              <div key={follower.id} className="relative">
                <UserCard
                  user={{
                    ...follower,
                    email: follower.email || '',
                    username: follower.email?.split('@')[0] || follower.id,
                    avatar: follower.image || '',
                    postCount: follower._count.posts,
                    followerCount: follower._count.followers,
                    followingCount: follower._count.following,
                    bio: follower.bio || '',
                    location: '',
                    isOnline: false,
                    badges: [],
                    communities: [],
                    joinedDate: new Date(follower.createdAt)
                  }}
                  onFollowChange={handleFollowChange}
                  variant="detailed"
                  showFollowButton={true}
                />
                <div className="absolute top-2 right-2">
                  <div className="tooltip tooltip-left" data-tip={`Followed you on ${formatFollowDate(follower.followedAt)}`}>
                    <div className="badge badge-primary badge-xs">
                      {formatFollowDate(follower.followedAt)}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
