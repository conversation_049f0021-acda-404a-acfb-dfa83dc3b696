"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import Link from "next/link";
import UserCard from "@/components/UserCard";
import { useFollowing } from "@/hooks/useFollowing";

// Mock data removed - using real API data only

export default function UsersPage() {
  const { data: session, status } = useSession();
  const { followingData, toggleFollow, isLoading: followingLoading } = useFollowing();
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"name" | "posts" | "followers" | "recent">("name");
  const [filterBy, setFilterBy] = useState<"all" | "online" | "following">("all");
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid');

  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/users/public?limit=50');
        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }
        const data = await response.json();
        // Use only real API fields
        const transformedUsers = data.users.map((user: any) => ({
          id: user.id,
          name: user.name || 'Anonymous User',
          username: user.name?.toLowerCase().replace(/\s+/g, '_') || 'anonymous',
          email: user.email || '',
          avatar: user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name || 'User')}&background=06b6d4&color=fff`,
          bio: user.bio || 'No bio available',
          postCount: user._count?.posts || 0,
          followerCount: user._count?.followers || 0,
          followingCount: user._count?.following || 0,
          isOnline: false, // Not available from API
          joinedDate: new Date(user.createdAt),
          isFollowing: false // Will be updated by following effect
        }));
        setUsers(transformedUsers);
        setError(null);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError('Failed to load users. Please try again.');
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, []);

  // Update users with current following status
  useEffect(() => {
    if (!followingLoading && followingData.size > 0) {
      console.log('Following data updated, size:', followingData.size);
      setUsers(prev =>
        prev.map(user => {
          const followingStatus = followingData.get(user.id);
          return {
            ...user,
            isFollowing: followingStatus?.isFollowing || false
          };
        })
      );
    }
  }, [followingData, followingLoading]);

  const handleFollowChange = async (userId: string, isFollowing: boolean) => {
    // Just update local state - FollowButton handles its own API calls
    setUsers(prev =>
      prev.map(user =>
        user.id === userId
          ? { ...user, isFollowing }
          : user
      )
    );
    
    // Let the FollowButton handle the API call, we just need to update our local state
    // The useFollowing hook will be refreshed by the FollowButton itself
  };

  const filteredUsers = users
    .filter(user => {
      const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           user.bio.toLowerCase().includes(searchQuery.toLowerCase());

      // Only filter by "following" (real), skip "online" and "followers" as not available
      const matchesFilter = filterBy === "all" ||
                           (filterBy === "following" && user.isFollowing);

      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "posts":
          return (b.postCount || 0) - (a.postCount || 0);
        // Remove followers sort as not available
        case "recent":
        default:
          return b.joinedDate.getTime() - a.joinedDate.getTime();
      }
    });

  // Show loading state while session, users, or following data is loading
  if (loading || status === "loading" || (session && followingLoading)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
        <div className="max-w-7xl mx-auto page-container">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="text-center">
              <div className="loading loading-spinner loading-lg text-primary mb-4"></div>
              <p className="text-base-content/70">
                {status === "loading" ? "Loading authentication..." : 
                 followingLoading ? "Loading follow data..." : 
                 "Loading community members..."}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
        <div className="max-w-7xl mx-auto page-container">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="text-center">
              <div className="text-6xl mb-4">⚠️</div>
              <h2 className="text-2xl font-bold text-error mb-4">Unable to Load Members</h2>
              <p className="text-base-content/70 mb-6">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="btn btn-primary"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
      <div className="max-w-7xl mx-auto page-container content-spacing">
        {/* Header */}
        <div className="enhanced-card">
          <div className="enhanced-card-body">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-2">
                  Com4 Members
                </h1>
                <p className="text-base-content/70 text-lg">
                  Discover and connect with amazing people in our community driven forum
                </p>
              </div>
              <div className="stats shadow-lg bg-gradient-to-r from-primary/5 to-accent/5">
                <div className="stat">
                  <div className="stat-title text-xs">Total Members</div>
                  <div className="stat-value text-2xl text-primary">{users.length || 0}</div>
                </div>
                <div className="stat">
                  <div className="stat-title text-xs">Following</div>
                  <div className="stat-value text-2xl text-info">
                    {followingLoading ? (
                      <span className="loading loading-spinner loading-sm"></span>
                    ) : (
                      followingData.size || 0
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Privacy Notice for Non-Authenticated Users */}
        {!session && (
          <div className="alert alert-info shadow-lg">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h3 className="font-bold">Limited View</h3>
              <div className="text-sm">
                You're viewing public profiles only.
                <Link href="/auth/signin" className="link link-primary font-semibold ml-1">
                  Sign in
                </Link> to see more member details and connect with the community.
              </div>
            </div>
          </div>
        )}

        {/* Search, Filters, and View Toggle */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
          <div className="card-body p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center">
              {/* Search */}
              <div className="flex-1">
                <div className="form-control">
                  <div className="input-group">
                    <span className="bg-base-200">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </span>
                    <input
                      type="text"
                      placeholder="Search members..."
                      className="input input-bordered w-full"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Sort */}
              <div className="form-control">
                <select
                  className="select select-bordered"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                >
                  <option value="name">Name (A-Z)</option>
                  <option value="posts">Most Posts</option>
                  <option value="recent">Recently Joined</option>
                </select>
              </div>

              {/* Filter */}
              <div className="form-control">
                <select
                  className="select select-bordered"
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as any)}
                >
                  <option value="all">All Members</option>
                  <option value="following">Following</option>
                </select>
              </div>

              {/* View Toggle */}
              <div className="form-control">
                <button
                  type="button"
                  className="btn btn-outline btn-sm"
                  onClick={() => setViewType(viewType === 'grid' ? 'list' : 'grid')}
                  aria-label="Toggle view"
                >
                  {viewType === 'grid' ? (
                    <span className="flex items-center gap-1"><svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" /></svg> List View</span>
                  ) : (
                    <span className="flex items-center gap-1"><svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h7v7H4V6zm9 0h7v7h-7V6zm0 9h7v7h-7v-7zm-9 0h7v7H4v-7z" /></svg> Grid View</span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Users Grid/List */}
        {viewType === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredUsers.length === 0 ? (
              <div className="col-span-full">
                <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
                  <div className="card-body text-center py-16">
                    <div className="text-6xl mb-4">👥</div>
                    <h3 className="text-2xl font-bold mb-2">No members found</h3>
                    <p className="text-base-content/70">
                      Try adjusting your search or filters.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              filteredUsers.map((user) => (
                <UserCard
                  key={user.id}
                  user={user}
                  onFollowChange={handleFollowChange}
                  variant="detailed"
                  showFollowButton={session?.user?.email !== user.email}
                />
              ))
            )}
          </div>
        ) : (
          <div className="flex flex-col gap-4">
            {filteredUsers.length === 0 ? (
              <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
                <div className="card-body text-center py-16">
                  <div className="text-6xl mb-4">👥</div>
                  <h3 className="text-2xl font-bold mb-2">No members found</h3>
                  <p className="text-base-content/70">
                    Try adjusting your search or filters.
                  </p>
                </div>
              </div>
            ) : (
              filteredUsers.map((user) => (
                <UserCard
                  key={user.id}
                  user={user}
                  onFollowChange={handleFollowChange}
                  variant="compact"
                  showFollowButton={session?.user?.email !== user.email}
                />
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
}
