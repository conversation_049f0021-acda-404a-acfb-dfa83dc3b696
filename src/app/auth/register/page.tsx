"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { signIn } from "next-auth/react";

interface FormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  bio: string;
  isPrivate: boolean;
  showEmail: boolean;
}

interface FormErrors {
  [key: string]: string;
}

export default function RegisterPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    bio: "",
    isPrivate: false,
    showEmail: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    } else if (formData.name.length < 2) {
      newErrors.name = "Name must be at least 2 characters";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = "Password must contain at least one uppercase letter, one lowercase letter, and one number";
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords don't match";
    }

    if (formData.bio && formData.bio.length > 500) {
      newErrors.bio = "Bio must be less than 500 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('JSON parsing error:', jsonError);
        setErrors({ general: 'Server error: Invalid response format' });
        return;
      }

      if (!response.ok) {
        if (data.details) {
          // Handle validation errors from server
          const serverErrors: FormErrors = {};
          data.details.forEach((detail: any) => {
            serverErrors[detail.field] = detail.message;
          });
          setErrors(serverErrors);
        } else {
          setErrors({ general: data.error || 'Registration failed' });
        }
        return;
      }

      setSuccess(true);
      
      // Auto-sign in after successful registration
      setTimeout(async () => {
        const result = await signIn("credentials", {
          email: formData.email,
          password: formData.password,
          redirect: false,
        });

        if (result?.ok) {
          router.push('/');
        } else {
          router.push('/auth/signin?message=Registration successful! Please sign in.');
        }
      }, 2000);

    } catch (error) {
      console.error('Registration error:', error);
      setErrors({ general: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200 via-base-100 to-base-200 flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center">
          <div className="card bg-gradient-to-br from-success/10 to-success/5 shadow-2xl border border-success/20">
            <div className="card-body p-8">
              <div className="flex flex-col items-center gap-4 mb-6">
                <img
                  src="/images/logo.png"
                  alt="COMFOR Logo"
                  className="w-16 h-16 object-contain"
                />
                <div className="text-4xl">🎉</div>
              </div>
              <h2 className="text-2xl font-bold text-success mb-4">Welcome to COMFOR!</h2>
              <p className="text-base-content/70 mb-6">
                Your account has been created successfully. You're being signed in automatically...
              </p>
              <div className="loading loading-spinner loading-lg text-success"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200 via-base-100 to-base-200 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-accent/5 to-secondary/5"></div>
      
      <div className="max-w-lg w-full relative z-10">
        <div className="text-center mb-8">
          <div className="flex flex-col items-center gap-4 mb-6">
            <img
              src="/images/logo.png"
              alt="COMFOR Logo"
              className="w-20 h-20 object-contain drop-shadow-2xl"
            />
          </div>
          <h2 className="text-3xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
            Join COMFOR
          </h2>
          <p className="mt-3 text-lg text-base-content/70">
            Create your account and become part of our community
          </p>
        </div>

        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body p-8">
            {errors.general && (
              <div className="alert alert-error mb-6">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {errors.general}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name Field */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold">Full Name *</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className={`input input-bordered w-full rounded-xl ${errors.name ? 'input-error' : ''}`}
                  placeholder="Enter your full name"
                  disabled={isLoading}
                />
                {errors.name && <span className="text-error text-sm mt-1">{errors.name}</span>}
              </div>

              {/* Email Field */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold">Email Address *</span>
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`input input-bordered w-full rounded-xl ${errors.email ? 'input-error' : ''}`}
                  placeholder="Enter your email address"
                  disabled={isLoading}
                />
                {errors.email && <span className="text-error text-sm mt-1">{errors.email}</span>}
              </div>

              {/* Password Field */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold">Password *</span>
                </label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className={`input input-bordered w-full rounded-xl ${errors.password ? 'input-error' : ''}`}
                  placeholder="Create a strong password"
                  disabled={isLoading}
                />
                {errors.password && <span className="text-error text-sm mt-1">{errors.password}</span>}
                <div className="text-xs text-base-content/60 mt-1">
                  Must be 8+ characters with uppercase, lowercase, and number
                </div>
              </div>

              {/* Confirm Password Field */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold">Confirm Password *</span>
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className={`input input-bordered w-full rounded-xl ${errors.confirmPassword ? 'input-error' : ''}`}
                  placeholder="Confirm your password"
                  disabled={isLoading}
                />
                {errors.confirmPassword && <span className="text-error text-sm mt-1">{errors.confirmPassword}</span>}
              </div>

              {/* Bio Field */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold">Bio (Optional)</span>
                </label>
                <textarea
                  name="bio"
                  value={formData.bio}
                  onChange={handleChange}
                  className={`textarea textarea-bordered w-full rounded-xl ${errors.bio ? 'textarea-error' : ''}`}
                  placeholder="Tell us a bit about yourself..."
                  rows={3}
                  disabled={isLoading}
                />
                {errors.bio && <span className="text-error text-sm mt-1">{errors.bio}</span>}
                <div className="text-xs text-base-content/60 mt-1">
                  {formData.bio.length}/500 characters
                </div>
              </div>

              {/* Privacy Settings */}
              <div className="space-y-4">
                <h3 className="font-semibold text-base-content">Privacy Settings</h3>
                
                <div className="form-control">
                  <label className="label cursor-pointer justify-start gap-3">
                    <input
                      type="checkbox"
                      name="isPrivate"
                      checked={formData.isPrivate}
                      onChange={handleChange}
                      className="checkbox checkbox-primary"
                      disabled={isLoading}
                    />
                    <div>
                      <span className="label-text font-medium">Private Profile</span>
                      <div className="text-xs text-base-content/60">
                        Hide your profile from public member directory
                      </div>
                    </div>
                  </label>
                </div>

                <div className="form-control">
                  <label className="label cursor-pointer justify-start gap-3">
                    <input
                      type="checkbox"
                      name="showEmail"
                      checked={formData.showEmail}
                      onChange={handleChange}
                      className="checkbox checkbox-primary"
                      disabled={isLoading}
                    />
                    <div>
                      <span className="label-text font-medium">Show Email Address</span>
                      <div className="text-xs text-base-content/60">
                        Allow other members to see your email address
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isLoading}
                className="btn btn-primary w-full rounded-2xl text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] disabled:hover:scale-100"
              >
                {isLoading ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Creating Account...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                    Create Account
                  </>
                )}
              </button>
            </form>

            <div className="divider text-base-content/50 font-medium">ALREADY HAVE AN ACCOUNT?</div>

            <div className="text-center">
              <Link 
                href="/auth/signin"
                className="btn btn-outline w-full rounded-2xl border-2 hover:bg-base-100 hover:border-primary/40 transition-all duration-300 hover:scale-[1.02] text-base font-medium"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                Sign In Instead
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
