"use client";

export const dynamic = 'force-static';

export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-base-200 via-base-300 to-base-200">
      <div className="max-w-md w-full space-y-6 card bg-base-100/80 backdrop-blur-xl shadow-2xl border border-primary/10 p-8">
        <h1 className="text-2xl font-bold text-center">Forgot Password</h1>
        <p className="text-sm text-base-content/70">
          Password reset isn&apos;t fully implemented yet. For demo accounts you can sign in with any password.
          If this were production we would:
        </p>
        <ul className="list-disc list-inside text-sm text-base-content/70 space-y-1">
          <li>Accept your email</li>
          <li>Create a signed, one-time reset token</li>
          <li>Email a reset link (e.g. /auth/reset?token=...)</li>
          <li>Let you set a new password if token valid & not expired</li>
        </ul>
  <form className="space-y-4" action="#" onSubmit={(e) => e.preventDefault()}>
          <label className="form-control">
            <span className="label-text font-medium mb-1">Email address</span>
            <input type="email" required className="input input-bordered w-full" placeholder="<EMAIL>" />
          </label>
          <button disabled className="btn btn-primary w-full">
            Coming Soon
          </button>
        </form>
        <p className="text-xs text-center text-base-content/50">
          This placeholder removes the 404. Implementing full reset flow would require email provider and secure tokens.
        </p>
      </div>
    </div>
  );
}
