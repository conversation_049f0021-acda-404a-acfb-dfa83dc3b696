"use client";
import Link from "next/link";

export default function PostsError({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  return (
    <div className="min-h-dvh flex flex-col items-center justify-center px-6 py-12 text-center gap-6">
      <div>
        <h1 className="text-5xl font-extrabold tracking-tight">Posts Error</h1>
        <p className="mt-3 opacity-70 max-w-md mx-auto">
          We hit a snag loading posts. You can try again.
        </p>
      <div className="flex gap-4">
        <button onClick={reset} className="btn btn-primary">Retry</button>
        { }
        <Link href="/" className="btn btn-outline">Home</Link>
      </div>
      </div>
      {error?.digest && (
        <p className="text-xs opacity-40">Ref: {error.digest}</p>
      )}
    </div>
  );
}
