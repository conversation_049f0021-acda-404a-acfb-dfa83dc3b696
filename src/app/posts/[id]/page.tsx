"use client";

import { useState, useEffect, useRef } from "react";
import { renderMarkdown } from '@/lib/markdown';
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { getRouteParam } from '@/lib/params';
import { io, Socket } from 'socket.io-client';

// Types
interface Post {
  id: string;
  title: string;
  content: string;
  published: boolean;
  images: string[];
  videoUrl: string | null;
  createdAt: string;
  updatedAt: string;
  authorId: string;
  communityId: string | null;
  author: {
    id: string;
    name: string;
    email: string;
    role: string;
    image: string;
  };
  community?: {
    id: string;
    name: string;
    slug: string;
  };
  comments: Comment[];
  _count: {
    comments: number;
    likes: number;
  };
}

interface Comment {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  authorId: string;
  postId: string;
  parentId: string | null;
  author: {
    id: string;
    name: string;
    email: string;
    role: string;
    image: string;
  };
  replies: Comment[];
  _count: {
    likes: number;
    replies: number;
  };
}

// Enhanced mock data for demonstration
const mockPosts = {
  "1": {
    id: "1",
    title: "🚀 Building the Future of Web Development: A Deep Dive into Modern Forum Architecture",
  content: `Welcome to our revolutionary forum platform! This isn't just another discussion board – it's a carefully crafted community space built with cutting-edge technologies and user experience in mind.

## 🎯 What Makes This Special?

Our platform represents the pinnacle of modern web development, combining performance, accessibility, and beautiful design into one cohesive experience.

### 🛠️ Technology Stack

We've carefully selected each technology for maximum impact:

**Frontend Excellence:**
- **Next.js 15** with App Router for lightning-fast navigation
- **TypeScript** ensuring rock-solid type safety
- **Tailwind CSS + DaisyUI** for stunning, consistent design
- **Framer Motion** for smooth, delightful animations

**Backend Power:**
- **Prisma ORM** for type-safe database operations
- **NextAuth.js** for secure, flexible authentication
- **PostgreSQL** for reliable data persistence
- **Redis** for blazing-fast caching

**Developer Experience:**
- **ESLint + Prettier** for consistent code quality
- **Husky** for automated pre-commit hooks
- **GitHub Actions** for seamless CI/CD
- **Vercel** for instant, global deployments

## 🌟 Key Features

### Real-time Interactions
Experience instant updates as conversations unfold. No more refreshing pages – everything happens live!

### Smart Notifications
Get notified about replies, mentions, and important updates without being overwhelmed.

### Advanced Search
Find exactly what you're looking for with our powerful search engine that understands context and intent.

### Accessibility First
Every component is built with accessibility in mind, ensuring everyone can participate fully.

## 🎨 Design Philosophy

We believe that great software should be:
- **Intuitive** - No learning curve required
- **Beautiful** - Aesthetics matter for user engagement
- **Fast** - Every millisecond counts
- **Inclusive** - Accessible to everyone

## 🚀 What's Next?

We're constantly evolving and adding new features based on community feedback. Some exciting additions coming soon:

- **AI-powered content suggestions**
- **Advanced moderation tools**
- **Mobile app with offline support**
- **Integration with popular developer tools**

---

*What do you think about our approach? We'd love to hear your thoughts and suggestions in the comments below!*`,
  author: {
    id: "admin-1",
    name: "Sarah Chen",
    role: "ADMIN",
    avatar: "https://ui-avatars.com/api/?name=Sarah+Chen&background=ef4444&color=fff",
    bio: "Lead Developer & Community Manager"
  },
  createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
  updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
  likes: 47,
  views: 234,
  published: true,
  tags: ["announcement", "technology", "web-development", "community"],
  category: "General",
  readTime: 8,
  isPinned: true,
},
"2": {
  id: "2",
  title: "🎨 Mastering CSS Grid and Flexbox: A Complete Guide",
  content: `CSS Grid and Flexbox are two of the most powerful layout systems in modern web development. Understanding when and how to use each one can dramatically improve your frontend development skills.

## 🔧 CSS Grid vs Flexbox

### When to Use CSS Grid
CSS Grid is perfect for two-dimensional layouts where you need to control both rows and columns:

- Complex page layouts
- Card-based designs
- Magazine-style layouts
- Dashboard interfaces

### When to Use Flexbox
Flexbox excels at one-dimensional layouts and component-level design:

- Navigation bars
- Button groups
- Centering content
- Distributing space between items

## 📚 Practical Examples

Let me show you some real-world examples that will help you master these technologies...`,
  author: {
    id: "user-2",
    name: "Sarah Chen",
    role: "EDITOR",
    avatar: "https://ui-avatars.com/api/?name=Sarah+Chen&background=10b981&color=fff",
    bio: "CSS Expert & Frontend Architect"
  },
  createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
  likes: 89,
  comments: 23,
  views: 1247,
  published: true,
  tags: ["css", "grid", "flexbox", "layout", "frontend"],
  category: "CSS & Styling",
  readTime: 5,
  isPinned: false
},
"3": {
  id: "3",
  title: "🔒 Security Best Practices for Modern Web Applications",
  content: `Security should never be an afterthought in web development. In this comprehensive guide, we'll explore the essential security practices every developer should implement.

## 🛡️ Core Security Principles

### Authentication & Authorization
- Implement proper session management
- Use secure password hashing (bcrypt, Argon2)
- Enable two-factor authentication
- Follow the principle of least privilege

### Data Protection
- Always validate and sanitize user input
- Use parameterized queries to prevent SQL injection
- Implement proper CORS policies
- Encrypt sensitive data at rest and in transit

### Frontend Security
- Prevent XSS attacks with proper output encoding
- Use Content Security Policy (CSP) headers
- Validate all client-side inputs on the server
- Keep dependencies updated

## 🚨 Common Vulnerabilities

Let's dive into the OWASP Top 10 and how to prevent each vulnerability...`,
  author: {
    id: "user-3",
    name: "Michael Torres",
    role: "ADMIN",
    avatar: "https://ui-avatars.com/api/?name=Michael+Torres&background=dc2626&color=fff",
    bio: "Security Engineer & DevOps Specialist"
  },
  createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
  likes: 156,
  comments: 34,
  views: 2103,
  published: true,
  tags: ["security", "authentication", "encryption", "best-practices"],
  category: "Security",
  readTime: 12,
  isPinned: false
}
};

const mockComments = [
  {
    id: "1",
    content: "This is absolutely incredible! 🤩 The attention to detail in both the technical architecture and user experience is outstanding. I'm particularly impressed with the choice to use Next.js 15 with the App Router - the performance improvements are noticeable immediately.",
    author: {
      id: "user-1",
      name: "Alex Rodriguez",
      role: "USER",
      avatar: "https://ui-avatars.com/api/?name=Alex+Rodriguez&background=06b6d4&color=fff",
      bio: "Full-stack Developer"
    },
    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
    likes: 12,
    parentId: null,
    replies: [
      {
        id: "2",
        content: "Totally agree! The DaisyUI integration is seamless. I've been struggling with component consistency in my own projects, and this is exactly the approach I needed to see. The luxury theme is *chef's kiss* 👌",
        author: {
          id: "user-2",
          name: "Maya Patel",
          role: "USER",
          avatar: "https://ui-avatars.com/api/?name=Maya+Patel&background=8b5cf6&color=fff",
          bio: "UI/UX Designer"
        },
        createdAt: new Date(Date.now() - 30 * 60 * 1000),
        likes: 8,
        parentId: "1",
        replies: []
      },
      {
        id: "4",
        content: "The accessibility-first approach really stands out. As someone who relies on screen readers, I can tell you put serious thought into making this inclusive. Thank you! 🙏",
        author: {
          id: "user-4",
          name: "Jordan Kim",
          role: "USER",
          avatar: "https://ui-avatars.com/api/?name=Jordan+Kim&background=10b981&color=fff",
          bio: "Accessibility Advocate"
        },
        createdAt: new Date(Date.now() - 15 * 60 * 1000),
        likes: 15,
        parentId: "1",
        replies: []
      }
    ]
  },
  {
    id: "3",
    content: "Fantastic work on the real-time features! 🚀 I'm curious about the technical implementation - are you using WebSockets, Server-Sent Events, or something else for the live updates? Also, any plans for offline support in the mobile app?",
    author: {
      id: "editor-1",
      name: "Chris Thompson",
      role: "EDITOR",
      avatar: "https://ui-avatars.com/api/?name=Chris+Thompson&background=f59e0b&color=fff",
      bio: "Senior Software Engineer"
    },
    createdAt: new Date(Date.now() - 45 * 60 * 1000),
    likes: 9,
    parentId: null,
    replies: [
      {
        id: "5",
        content: "Great question! We're using a hybrid approach with WebSockets for real-time interactions and Server-Sent Events for notifications. The offline support is definitely on our roadmap - we're exploring service workers and IndexedDB for local caching. 📱",
        author: {
          id: "admin-1",
          name: "Sarah Chen",
          role: "ADMIN",
          avatar: "https://ui-avatars.com/api/?name=Sarah+Chen&background=ef4444&color=fff",
          bio: "Lead Developer & Community Manager"
        },
        createdAt: new Date(Date.now() - 20 * 60 * 1000),
        likes: 18,
        parentId: "3",
        replies: []
      }
    ]
  },
  {
    id: "6",
    content: "Love the modern tech stack! 💻 Quick question though - how are you handling SEO with the App Router? I've been having some challenges with dynamic routes and metadata generation in my Next.js projects.",
    author: {
      id: "user-3",
      name: "Emma Wilson",
      role: "USER",
      avatar: "https://ui-avatars.com/api/?name=Emma+Wilson&background=ec4899&color=fff",
      bio: "Frontend Developer"
    },
    createdAt: new Date(Date.now() - 25 * 60 * 1000),
    likes: 6,
    parentId: null,
    replies: []
  }
];

export default function PostDetailPage() {
  const { data: session } = useSession();
  const params = useParams();
  const postId = getRouteParam(params, 'id');
  const [post, setPost] = useState<Post | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const [likedPosts, setLikedPosts] = useState<Set<string>>(new Set());
  const [likedComments, setLikedComments] = useState<Set<string>>(new Set());
  const socketRef = useState<Socket | null>(null)[0];
  const [views, setViews] = useState<number | null>(null);
  const incrementedRef = useRef(false);

  // Increment view count once per mount (client-side) & set local state
  useEffect(() => {
    if (!postId || incrementedRef.current) return;
    incrementedRef.current = true;
    fetch(`/api/posts/${postId}/views`, { method: 'POST' })
      .then(r => r.ok ? r.json() : Promise.reject())
      .then(data => setViews(typeof data?.views === 'number' ? data.views : 0))
      .catch(() => setViews(0));
  }, [postId]);

  // Establish socket connection & join post room
  useEffect(() => {
    if (!postId) return;
    // Lazy attach single socket to window for reuse
    let socket: Socket;
    if (typeof window !== 'undefined') {
      // Reuse existing global socket if present
      socket = (window as any).__postSocket || io(process.env.NEXT_PUBLIC_REALTIME_URL || 'http://localhost:4001', {
        transports: ['websocket', 'polling'],
        reconnectionAttempts: 5,
      });
      (window as any).__postSocket = socket;

      socket.emit('join', `post:${postId}`);

      // New top-level comment from others
      const onNewComment = (payload: any) => {
        if (!payload?.comment) return;
        setComments(prev => {
          // Avoid duplicating if already exists (id match)
            if (prev.some(c => c.id === payload.comment.id)) return prev;
            return [normalizeComment(payload.comment), ...prev];
        });
        setPost(prev => prev ? ({ ...prev, _count: { ...prev._count, comments: prev._count.comments + 1 } }) : prev);
      };
      // Reply appended
      const onReply = (payload: any) => {
        if (!payload?.comment || !payload?.parentId) return;
        setComments(prev => {
          const addReply = (nodes: Comment[]): Comment[] => nodes.map(c => {
            if (c.id === payload.parentId) {
              const existing = Array.isArray(c.replies) ? c.replies : [];
              if (existing.some(r => r.id === payload.comment.id)) return c; // already added
              const updated = [...existing, normalizeComment(payload.comment)];
              return { ...c, replies: updated, _count: { ...c._count, replies: c._count.replies + 1 } };
            }
            if (c.replies?.length) return { ...c, replies: addReply(c.replies) };
            return c;
          });
          return addReply(prev);
        });
      };

      socket.on('comment:new', onNewComment);
      socket.on('comment:reply', onReply);

      return () => {
        socket.off('comment:new', onNewComment);
        socket.off('comment:reply', onReply);
      };
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postId]);

  // Fetch post data
  const fetchPost = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/posts/${postId}`);
      if (!response.ok) {
        throw new Error('Post not found');
      }
      const data: Post = await response.json();
      setPost(data);
  setComments(normalizeComments(data.comments || []));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch post');
    } finally {
      setLoading(false);
    }
  };

  // Fetch comments separately if needed
  const fetchComments = async () => {
    try {
      setCommentsLoading(true);
      const response = await fetch(`/api/comments?postId=${postId}`);
      if (response.ok) {
        const data = await response.json();
  setComments(normalizeComments(data.comments || []));
      }
    } catch (err) {
      console.error('Error fetching comments:', err);
    } finally {
      setCommentsLoading(false);
    }
  };

  // Load post on component mount
  useEffect(() => {
    if (postId) {
      fetchPost();
    }
  }, [postId]);

  // Handle like post
  const handleLikePost = async () => {
    if (!session || !post) {
      alert('Please sign in to like posts');
      return;
    }

    try {
      const response = await fetch('/api/likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ postId: post.id }),
      });

      if (!response.ok) {
        throw new Error('Failed to toggle like');
      }

      const data = await response.json();

      // Update local state
      const newLikedPosts = new Set(likedPosts);
      if (data.liked) {
        newLikedPosts.add(post.id);
      } else {
        newLikedPosts.delete(post.id);
      }
      setLikedPosts(newLikedPosts);

      // Update post like count
      setPost({
        ...post,
        _count: { ...post._count, likes: data.count }
      });
    } catch (error) {
      console.error('Error toggling like:', error);
      alert('Failed to toggle like. Please try again.');
    }
  };

  // Handle comment submission
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!session || !post || !newComment.trim()) {
      return;
    }

    try {
      // Optimistic placeholder id
      const optimisticId = `temp-${Date.now()}`;
      const optimistic: Comment = normalizeComment({
        id: optimisticId,
        content: newComment.trim(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        authorId: (session.user as any)?.id || '',
        postId: post.id,
        parentId: null,
        author: {
          id: (session.user as any)?.id || '',
          name: session.user?.name || 'You',
          email: session.user?.email || '',
          role: (session.user as any)?.role || 'USER',
          image: session.user?.image || ''
        },
        replies: [],
        _count: { likes: 0, replies: 0 }
      });
      setComments(prev => [optimistic, ...prev]);
      setNewComment("");
      setPost(p => p ? { ...p, _count: { ...p._count, comments: p._count.comments + 1 } } : p);

      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newComment.trim(),
          postId: post.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to post comment');
      }

  const newCommentData = await response.json();
  // Replace optimistic
  setComments(prev => prev.map(c => c.id === optimisticId ? normalizeComment(newCommentData) : c));

  // Avoid double increment; count already reflected by optimistic insertion
    } catch (error) {
      console.error('Error posting comment:', error);
  // Rollback optimistic
  setComments(prev => prev.filter(c => !c.id.startsWith('temp-')));
  setPost(p => p ? { ...p, _count: { ...p._count, comments: Math.max(0, p._count.comments - 1) } } : p);
  alert('Failed to post comment. Please try again.');
    }
  };

  // Handle reply submission
  const handleSubmitReply = async (e: React.FormEvent, parentId: string) => {
    e.preventDefault();
    if (!session || !post || !replyContent.trim()) {
      return;
    }

    try {
      const optimisticId = `temp-${Date.now()}`;
      const optimisticReply: Comment = normalizeComment({
        id: optimisticId,
        content: replyContent.trim(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        authorId: (session.user as any)?.id || '',
        postId: post.id,
        parentId,
        author: {
          id: (session.user as any)?.id || '',
          name: session.user?.name || 'You',
          email: session.user?.email || '',
          role: (session.user as any)?.role || 'USER',
          image: session.user?.image || ''
        },
        replies: [],
        _count: { likes: 0, replies: 0 }
      });

      // Optimistically insert reply
      setComments(prev => {
        const add = (nodes: Comment[]): Comment[] => nodes.map(c => {
          if (c.id === parentId) {
            const existing = Array.isArray(c.replies) ? c.replies : [];
            return { ...c, replies: [...existing, optimisticReply], _count: { ...c._count, replies: c._count.replies + 1 } };
          }
          if (c.replies?.length) return { ...c, replies: add(c.replies) };
          return c;
        });
        return add(prev);
      });
      setReplyContent("");
      setReplyingTo(null);

      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: replyContent.trim(),
          postId: post.id,
          parentId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to post reply');
      }

      const newReply = normalizeComment(await response.json());
      setComments(prev => {
        const replace = (nodes: Comment[]): Comment[] => nodes.map(c => {
          if (c.id === parentId) {
            return { ...c, replies: c.replies.map(r => r.id === optimisticId ? newReply : r) };
          }
          if (c.replies?.length) return { ...c, replies: replace(c.replies) };
          return c;
        });
        return replace(prev);
      });
    } catch (error) {
      console.error('Error posting reply:', error);
      // Rollback optimistic reply
      setComments(prev => {
        const rollback = (nodes: Comment[]): Comment[] => nodes.map(c => {
          if (c.id === parentId) {
            return { ...c, replies: c.replies.filter(r => !r.id.startsWith('temp-')), _count: { ...c._count, replies: Math.max(0, c._count.replies - 1) } };
          }
          if (c.replies?.length) return { ...c, replies: rollback(c.replies) };
          return c;
        });
        return rollback(prev);
      });
      alert('Failed to post reply. Please try again.');
    }
  };

  // Normalization helpers to guarantee replies is always an array
  function normalizeComment(raw: any): Comment {
    return {
      ...raw,
      replies: Array.isArray(raw.replies) ? raw.replies.map(normalizeComment) : [],
      _count: {
        likes: raw?._count?.likes ?? 0,
        replies: raw?._count?.replies ?? (Array.isArray(raw.replies) ? raw.replies.length : 0)
      }
    } as Comment;
  }

  function normalizeComments(list: any[]): Comment[] {
    return Array.isArray(list) ? list.map(normalizeComment) : [];
  }

  // Helper functions
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return "Less than an hour ago";
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "ADMIN": return "badge-error";
      case "EDITOR": return "badge-warning";
      default: return "badge-info";
    }
  };

  const getVideoEmbedUrl = (url: string) => {
    if (!url) return null;

    // YouTube
    const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
    if (youtubeMatch) {
      return `https://www.youtube.com/embed/${youtubeMatch[1]}`;
    }

    // Vimeo
    const vimeoMatch = url.match(/vimeo\.com\/(\d+)/);
    if (vimeoMatch) {
      return `https://player.vimeo.com/video/${vimeoMatch[1]}`;
    }

    return null;
  };

  const handleCommentLike = async (commentId: string) => {
    if (!session) {
      alert('Please sign in to like comments');
      return;
    }

    try {
      const response = await fetch('/api/likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ commentId }),
      });

      if (!response.ok) {
        throw new Error('Failed to toggle like');
      }

      const data = await response.json();

      // Update local state
      const newLikedComments = new Set(likedComments);
      if (data.liked) {
        newLikedComments.add(commentId);
      } else {
        newLikedComments.delete(commentId);
      }
      setLikedComments(newLikedComments);

      // Update comment like count
      const updateCommentLikes = (comments: Comment[]): Comment[] => {
        return comments.map(comment => {
          if (comment.id === commentId) {
            return { ...comment, _count: { ...comment._count, likes: data.count } };
          }
          if (comment.replies.length > 0) {
            return { ...comment, replies: updateCommentLikes(comment.replies) };
          }
          return comment;
        });
      };

      setComments(updateCommentLikes(comments));
    } catch (error) {
      console.error('Error toggling comment like:', error);
      alert('Failed to toggle like. Please try again.');
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
        <div className="container mx-auto px-4 py-8">
          <div className="card bg-base-100 shadow-xl border border-base-300">
            <div className="card-body text-center py-16">
              <div className="loading loading-spinner loading-lg text-primary"></div>
              <p className="text-base-content/60 mt-4">Loading post...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !post) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
        <div className="container mx-auto px-4 py-8">
          <div className="card bg-base-100 shadow-xl border border-error/20">
            <div className="card-body text-center py-16">
              <div className="text-6xl mb-4">⚠️</div>
              <h3 className="text-2xl font-bold mb-2 text-error">Post Not Found</h3>
              <p className="text-base-content/60 mb-6">{error || 'The post you are looking for does not exist.'}</p>
              <Link href="/posts" className="btn btn-primary">
                Back to Posts
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const CommentComponent = ({ comment, isReply = false }: { comment: Comment, isReply?: boolean }) => (
    <div className={`${isReply ? 'ml-8 border-l-2 border-primary/20 pl-6' : ''} group`}>
      <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-lg hover:shadow-xl transition-all duration-300 border border-base-300/50 hover:border-primary/20">
        <div className="card-body p-6">
          {/* Comment Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-4">
              <div className="avatar">
                <div className="w-10 h-10 rounded-full ring-2 ring-primary/20">
                  <img
                    src={comment.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.author.name)}&background=random`}
                    alt={comment.author.name}
                    className="rounded-full"
                  />
                </div>
              </div>
              <div>
                <div className="flex items-center space-x-3 flex-wrap">
                  <span className="font-bold text-base">{comment.author.name}</span>
                  <span className={`badge ${getRoleBadgeColor(comment.author.role)} badge-sm`}>
                    {comment.author.role}
                  </span>
                </div>
                <div className="flex items-center space-x-2 mt-1 text-sm text-base-content/60">
                  <span>{formatTimeAgo(comment.createdAt)}</span>
                  {comment.createdAt !== comment.updatedAt && (
                    <>
                      <span className="w-1 h-1 bg-base-content/30 rounded-full"></span>
                      <span>edited</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Comment Actions Dropdown */}
            <div className="dropdown dropdown-end opacity-0 group-hover:opacity-100 transition-opacity">
              <div tabIndex={0} role="button" className="btn btn-ghost btn-sm btn-circle">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </div>
              <ul tabIndex={0} className="dropdown-content z-[50] menu p-2 shadow-2xl bg-base-100 rounded-2xl w-48 border border-primary/10">
                <li><button className="rounded-xl hover:bg-primary/10 w-full text-left">📤 Share</button></li>
                <li><button className="rounded-xl hover:bg-primary/10 w-full text-left">🔗 Copy Link</button></li>
                <li><button className="rounded-xl hover:bg-primary/10 w-full text-left">🔖 Save</button></li>
                <div className="divider my-1"></div>
                <li><button className="rounded-xl hover:bg-error/10 text-error w-full text-left">🚨 Report</button></li>
              </ul>
            </div>
          </div>

          {/* Comment Content */}
          <div className="prose prose-sm max-w-none mb-4">
            <p className="whitespace-pre-wrap leading-relaxed">{comment.content}</p>
          </div>

          {/* Comment Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => handleCommentLike(comment.id)}
                className={`flex items-center space-x-2 transition-colors duration-300 group/like ${
                  likedComments.has(comment.id) ? 'text-error' : 'hover:text-error'
                }`}
              >
                <div className="p-1.5 rounded-full group-hover/like:bg-error/10 transition-colors duration-300">
                  <svg className={`w-4 h-4 ${likedComments.has(comment.id) ? 'fill-current' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <span className="font-medium text-sm">{comment._count.likes}</span>
              </button>

              {session?.user && !isReply && (
                <button
                  onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                  className={`flex items-center space-x-2 transition-colors duration-300 ${
                    replyingTo === comment.id ? 'text-primary' : 'hover:text-primary'
                  }`}
                >
                  <div className="p-1.5 rounded-full hover:bg-primary/10 transition-colors duration-300">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                    </svg>
                  </div>
                  <span className="font-medium text-sm">Reply</span>
                </button>
              )}
            </div>

            {comment.replies && comment.replies.length > 0 && (
              <div className="text-sm text-base-content/60">
                {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-6 space-y-4">
          {comment.replies.map((reply: any) => (
            <CommentComponent key={reply.id} comment={reply} isReply={true} />
          ))}
        </div>
      )}

      {/* Enhanced Reply Form */}
      {replyingTo === comment.id && session?.user && (
        <div className="mt-6 ml-8">
          <div className="card bg-base-100 shadow-lg border border-primary/20">
            <div className="card-body p-4">
              <div className="flex items-center space-x-3 mb-3">
                <div className="avatar">
                  <div className="w-8 h-8 rounded-full">
                    <img
                      src={session.user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.user.name || 'User')}&background=random`}
                      alt={session.user.name || 'User'}
                      className="rounded-full"
                    />
                  </div>
                </div>
                <span className="text-sm text-base-content/70">
                  Replying to <span className="font-semibold text-primary">{comment.author.name}</span>
                </span>
              </div>

              <form onSubmit={(e) => handleSubmitReply(e, comment.id)} className="space-y-3">
                <textarea
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  placeholder={`Reply to ${comment.author.name}...`}
                  className="textarea textarea-bordered w-full h-24 resize-none focus:border-primary/40 transition-colors"
                  required
                />
                <div className="flex justify-between items-center">
                  <div className="text-xs text-base-content/50">
                    {replyContent.length}/1000 characters
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={() => {
                        setReplyingTo(null);
                        setReplyContent("");
                      }}
                      className="btn btn-ghost btn-sm rounded-full"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary btn-sm rounded-full"
                      disabled={!replyContent.trim()}
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      Reply
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
      <div className="page-container">
        {/* Navigation */}
        <div className="flex items-center justify-between page-header">
          <Link href="/posts" className="btn btn-ghost rounded-full hover:bg-primary/10 transition-all duration-300 group">
            <svg className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
            Back to Posts
          </Link>

        </div>

        {/* Post Content */}
        <article className="enhanced-card hover:border-primary/20 section-spacing">
          <div className="enhanced-card-body">
            {/* Post Header */}
            <header className="mb-8">
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <div className="avatar">
                    <div className="w-16 h-16 rounded-full ring-4 ring-primary/20 ring-offset-2 ring-offset-base-100">
                      <img
                        src={post.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(post.author.name)}&background=random`}
                        alt={post.author.name}
                        className="rounded-full"
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center space-x-3 flex-wrap mb-2">
                      <h2 className="text-xl font-bold">{post.author.name}</h2>
                      <span className={`badge ${getRoleBadgeColor(post.author.role)} badge-lg`}>
                        {post.author.role}
                      </span>
                      {post.community && (
                        <Link href={`/communities/${post.community.slug}`} className="badge badge-outline hover:badge-primary">
                          {post.community.name}
                        </Link>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-base-content/60">
                      <span>📅 {formatTimeAgo(post.createdAt)}</span>
                      {post.updatedAt && post.updatedAt !== post.createdAt && (
                        <>
                          <span className="w-1 h-1 bg-base-content/30 rounded-full"></span>
                          <span>✏️ Edited {formatTimeAgo(post.updatedAt)}</span>
                        </>
                      )}
                      <span className="w-1 h-1 bg-base-content/30 rounded-full"></span>
                      <span>⏱️ {Math.floor((post.content?.length || 0) / 200) + 1} min read</span>
                    </div>
                  </div>
                </div>
              </div>
            </header>

            {/* Post Title */}
            <div className="mb-8">
              <h1 className="text-3xl lg:text-4xl font-black leading-tight mb-4 bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
                {post.title}
              </h1>
            </div>

            {/* Post Content (Markdown) */}
            <div className="prose prose-lg max-w-none mb-8">
              <div
                className="leading-relaxed text-base-content/90"
                dangerouslySetInnerHTML={{ __html: renderMarkdown(post.content || '') }}
              />
            </div>

            {/* Post Images */}
            {post.images && post.images.length > 0 && (
              <div className="mb-8">
                <div className={`grid gap-4 ${
                  post.images.length === 1 ? 'grid-cols-1' :
                  post.images.length === 2 ? 'grid-cols-1 md:grid-cols-2' :
                  'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                }`}>
                  {post.images.map((image, index) => (
                    <div key={index} className="group cursor-pointer">
                      <img
                        src={image}
                        alt={`Post image ${index + 1}`}
                        className="w-full h-64 object-cover rounded-xl border border-base-300 group-hover:shadow-lg transition-all duration-300"
                        onClick={() => window.open(image, '_blank')}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Post Video */}
            {post.videoUrl && (
              <div className="mb-8">
                <div className="aspect-video bg-base-200 rounded-xl overflow-hidden border border-base-300 shadow-lg">
                  <iframe
                    src={getVideoEmbedUrl(post.videoUrl) || ''}
                    className="w-full h-full"
                    frameBorder="0"
                    allowFullScreen
                    title="Post video"
                  />
                </div>
              </div>
            )}

            {/* Action Bar */}
            <div className="flex items-center justify-between pt-6 border-t border-base-300">
              <div className="flex items-center space-x-6">
                {/* Like Button */}
                <button
                  onClick={handleLikePost}
                  className={`flex items-center space-x-3 transition-colors duration-300 group/like ${
                    likedPosts.has(post.id) ? 'text-error' : 'hover:text-error'
                  }`}
                >
                  <div className="p-3 rounded-full group-hover/like:bg-error/10 transition-colors duration-300">
                    <svg className={`w-6 h-6 ${likedPosts.has(post.id) ? 'fill-current' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg">{post._count.likes}</div>
                    <div className="text-xs text-base-content/60">likes</div>
                  </div>
                </button>

                {/* Comments Count */}
                <div className="flex items-center space-x-3 text-info">
                  <div className="p-3 rounded-full bg-info/10">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg">{comments.length}</div>
                    <div className="text-xs text-base-content/60">comments</div>
                  </div>
                </div>

                {/* Views Count */}
                <div className="flex items-center space-x-3 text-secondary">
                  <div className="p-3 rounded-full bg-secondary/10">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg">{views ?? '–'}</div>
                    <div className="text-xs text-base-content/60">views</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </article>

        {/* Comments Section */}
        <section className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body p-8">
            {/* Comments Header */}
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-4">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                  Discussion
                </h2>
                <div className="badge badge-primary badge-lg">
                  {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
                </div>
              </div>
            </div>

            {/* New Comment Form */}
            {session?.user ? (
              <div className="mb-10">
                <div className="card bg-base-100 shadow-lg border border-primary/20">
                  <div className="card-body p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="avatar">
                        <div className="w-12 h-12 rounded-full ring-2 ring-primary/20">
                          <img
                            src={session.user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.user.name || 'User')}&background=random`}
                            alt={session.user.name || 'User'}
                            className="rounded-full"
                          />
                        </div>
                      </div>
                      <div>
                        <div className="font-bold">{session.user.name}</div>
                        <div className="text-sm text-base-content/60">Share your thoughts on this post</div>
                      </div>
                    </div>

                    <form onSubmit={handleSubmitComment} className="space-y-4">
                      <textarea
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="What are your thoughts? Be respectful and constructive..."
                        className="textarea textarea-bordered w-full h-32 resize-none focus:border-primary/40 transition-colors"
                        maxLength={1000}
                        required
                      />

                      <div className="flex justify-between items-center">
                        <div className="text-xs text-base-content/50">
                          {newComment.length}/1000 characters
                        </div>
                        <div className="flex space-x-3">
                          <button
                            type="button"
                            onClick={() => setNewComment("")}
                            className="btn btn-ghost btn-sm rounded-full"
                            disabled={!newComment.trim()}
                          >
                            Clear
                          </button>
                          <button
                            type="submit"
                            className="btn btn-primary btn-sm rounded-full px-6"
                            disabled={!newComment.trim()}
                          >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                            Post Comment
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            ) : (
              <div className="card bg-gradient-to-br from-base-200 to-base-300/50 shadow-lg border border-primary/20">
                <div className="card-body text-center py-12">
                  <div className="text-6xl mb-4">💬</div>
                  <h3 className="text-2xl font-bold mb-2">Join the Discussion</h3>
                  <p className="text-base-content/70 mb-6">
                    Sign in to share your thoughts and engage with the community
                  </p>
                  <Link href="/auth/signin" className="btn btn-primary btn-lg rounded-full px-8">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Sign In to Comment
                  </Link>
                </div>
              </div>
            )}

            {/* Comments List */}
            {comments.length > 0 ? (
              <div className="space-y-8">
                <div className="divider">
                  <span className="text-base-content/60">
                    {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
                  </span>
                </div>

                {comments.map((comment, index) => (
                  <div key={comment.id} className="relative">
                    {index > 0 && <div className="divider opacity-30"></div>}
                    <CommentComponent comment={comment} />
                  </div>
                ))}
              </div>
            ) : (
              <div className="card bg-base-200/50 border border-dashed border-base-300">
                <div className="card-body text-center py-16">
                  <div className="text-8xl mb-6 opacity-20">💭</div>
                  <h3 className="text-2xl font-bold mb-3">No comments yet</h3>
                  <p className="text-base-content/60 mb-6 max-w-md mx-auto">
                    Be the first to share your thoughts on this post. Your insights could spark an amazing discussion!
                  </p>
                  {session?.user && (
                    <button
                      onClick={() => document.querySelector('textarea')?.focus()}
                      className="btn btn-primary btn-outline rounded-full"
                    >
                      Start the Discussion
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </section>


      </div>
    </div>
  );
}
