"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import Link from "next/link";
import { useSession } from "next-auth/react";

// Types
interface Post {
  id: string;
  title: string;
  content: string;
  published: boolean;
  images: string[];
  videoUrl: string | null;
  createdAt: string;
  updatedAt: string;
  authorId: string;
  author: {
    id: string;
    name: string;
    email: string;
    role: string;
    image: string;
  };
  community?: {
    id: string;
    name: string;
    slug: string;
  };
  _count: {
    comments: number;
    likes: number;
  };
}

interface PostsResponse {
  posts: Post[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Mock data removed - using real API data only

export default function PostsPage() {
  const { data: session } = useSession();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState("recent");
  const [filterBy, setFilterBy] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [viewMode, setViewMode] = useState<"card" | "list">("card");
  const [likedPosts, setLikedPosts] = useState<Set<string>>(new Set());
  const [viewCounts, setViewCounts] = useState<Record<string, number>>({});
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });

  // Fetch posts from API
  const fetchPosts = useCallback(async (page = 1, search = "", published = true) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        published: published.toString(),
      });

      if (search) {
        params.append('search', search);
      }

      const response = await fetch(`/api/posts?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch posts');
      }

      const data: PostsResponse = await response.json();
      setPosts(data.posts);
      setPagination(data.pagination);

      // Fetch view counts for this page of posts
      const ids = data.posts.map(p => p.id);
      if (ids.length) {
        try {
          const res = await fetch('/api/posts/views', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ids }),
          });
          if (res.ok) {
            const json = await res.json();
            setViewCounts(json.counts || {});
          } else {
            setViewCounts({});
          }
        } catch {
          setViewCounts({});
        }
      } else {
        setViewCounts({});
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch posts');
      // Don't set mock data as it doesn't match the Post interface
      setPosts([]);
    } finally {
      setLoading(false);
    }
  }, [pagination.limit]);

  // Load posts on component mount
  useEffect(() => {
    fetchPosts(1, "", true); // Fetch published posts
  }, [fetchPosts]);

  // Handle search
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchPosts(1, searchQuery, true);
    }, 500);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery, fetchPosts]);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return "Less than an hour ago";
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "ADMIN": return "badge-error";
      case "EDITOR": return "badge-warning";
      default: return "badge-info";
    }
  };

  const handleLike = async (postId: string) => {
    if (!session) {
      alert('Please sign in to like posts');
      return;
    }

    try {
      const response = await fetch('/api/likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ postId }),
      });

      if (!response.ok) {
        throw new Error('Failed to toggle like');
      }

      const data = await response.json();

      // Update local state
      const newLikedPosts = new Set(likedPosts);
      if (data.liked) {
        newLikedPosts.add(postId);
      } else {
        newLikedPosts.delete(postId);
      }
      setLikedPosts(newLikedPosts);

      // Update post like count
      setPosts(posts.map(post =>
        post.id === postId
          ? { ...post, _count: { ...post._count, likes: data.count } }
          : post
      ));
    } catch (error) {
      console.error('Error toggling like:', error);
      alert('Failed to toggle like. Please try again.');
    }
  };

  const handleShare = async (postId: string) => {
    const post = posts.find(p => p.id === postId);
    const shareData = {
      title: post?.title || 'Check out this post',
      text: post?.content.substring(0, 100) + '...' || '',
      url: `${window.location.origin}/posts/${postId}`
    };

    if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
      try {
        await navigator.share(shareData);
      } catch {
        // User cancelled share; no action needed
      }
    } else {
      // Fallback: copy to clipboard
      await navigator.clipboard.writeText(shareData.url);
      alert('Link copied to clipboard!');
    }
  };



  // Get unique categories (simplified for now)
  const getCategories = () => {
    return ["all", "General", "Development", "Security"];
  };

  // Filter and sort posts
  const getFilteredAndSortedPosts = () => {
    let filteredPosts = posts;

    // Filter by search query (search is handled server-side, but we can still filter locally)
    if (searchQuery && posts.length > 0) {
      filteredPosts = filteredPosts.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.author.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by type
    if (filterBy === "popular") {
      filteredPosts = filteredPosts.filter(post => post._count.likes > 5);
    } else if (filterBy === "trending") {
      filteredPosts = filteredPosts.filter(post => post._count.comments > 2);
    }

    // Sort posts
    switch (sortBy) {
      case "liked":
        return filteredPosts.sort((a, b) => b._count.likes - a._count.likes);
      case "commented":
        return filteredPosts.sort((a, b) => b._count.comments - a._count.comments);
      case "trending":
        return filteredPosts.sort((a, b) => (b._count.likes + b._count.comments) - (a._count.likes + a._count.comments));
      case "recent":
      default:
        return filteredPosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }
  };

  return (
    <div className="content-spacing">
      {/* Enhanced Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 page-header">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-4xl lg:text-5xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
              Community Posts
            </h1>
            <div className="badge badge-primary badge-lg">
              {getFilteredAndSortedPosts().length}
            </div>
          </div>
          <p className="text-lg text-base-content/70 mb-4">Discover amazing discussions and share your thoughts</p>

          {/* Stats Row */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
              <span className="text-sm text-success font-medium">Live Updates</span>
            </div>
            <div className="flex items-center space-x-4 text-sm text-base-content/60">
              <div className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <span><span className="font-semibold text-primary">{posts.reduce((sum, post) => sum + post._count.comments, 0)}</span> comments</span>
              </div>
              <div className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                <span><span className="font-semibold text-accent">{posts.reduce((sum, post) => sum + post._count.likes, 0)}</span> likes</span>
              </div>
              <div className="flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <span>
                  <span className="font-semibold text-secondary">
                    {posts.reduce((sum, p) => sum + (viewCounts[p.id] ?? 0), 0)}
                  </span> views
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="join">
            <button
              onClick={() => setViewMode("card")}
              className={`btn btn-sm join-item ${viewMode === "card" ? "btn-primary" : "btn-outline"}`}
              title="Card View"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`btn btn-sm join-item ${viewMode === "list" ? "btn-primary" : "btn-outline"}`}
              title="List View"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          </div>

          {session?.user && (
            <Link href="/posts/create" className="btn btn-primary btn-lg rounded-full px-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 group">
              <svg className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
              </svg>
              Create Post
            </Link>
          )}
        </div>
      </div>

      {/* Enhanced Filter/Sort Options */}
      <div className="enhanced-card">
        <div className="enhanced-card-body">
          {/* Top Row - Sort and Category */}
          <div className="flex flex-wrap items-center justify-between gap-4 mb-4">
            <div className="flex flex-wrap items-center gap-3">
              {/* Sort Dropdown */}
              <div className="dropdown">
                <div tabIndex={0} role="button" className="btn btn-outline rounded-full hover:bg-primary/10 border-primary/20 hover:border-primary/40 transition-all duration-300">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
                  </svg>
                  Sort: {sortBy === "recent" ? "Recent" : sortBy === "liked" ? "Most Liked" : sortBy === "commented" ? "Most Commented" : "Trending"}
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                <ul tabIndex={0} className="dropdown-content z-[50] menu p-2 shadow-2xl bg-base-100 rounded-2xl w-52 border border-primary/10">
                  <li><button onClick={() => setSortBy("recent")} className="rounded-xl hover:bg-primary/10 w-full text-left">📅 Most Recent</button></li>
                  <li><button onClick={() => setSortBy("liked")} className="rounded-xl hover:bg-primary/10 w-full text-left">❤️ Most Liked</button></li>
                  <li><button onClick={() => setSortBy("commented")} className="rounded-xl hover:bg-primary/10 w-full text-left">💬 Most Commented</button></li>
                  <li><button onClick={() => setSortBy("trending")} className="rounded-xl hover:bg-primary/10 w-full text-left">🔥 Trending</button></li>
                </ul>
              </div>

              {/* Category Dropdown */}
              <div className="dropdown">
                <div tabIndex={0} role="button" className="btn btn-outline rounded-full hover:bg-secondary/10 border-secondary/20 hover:border-secondary/40 transition-all duration-300">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  Category: {selectedCategory === "all" ? "All" : selectedCategory}
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                <ul tabIndex={0} className="dropdown-content z-[50] menu p-2 shadow-2xl bg-base-100 rounded-2xl w-52 border border-primary/10">
                  {getCategories().map(category => (
                    <li key={category}>
                      <button
                        onClick={() => setSelectedCategory(category)}
                        className={`rounded-xl hover:bg-secondary/10 w-full text-left ${selectedCategory === category ? 'bg-secondary/20 text-secondary' : ''}`}
                      >
                        {category === "all" ? "📂 All Categories" :
                         category === "General" ? "💬 General" :
                         category === "Development" ? "💻 Development" :
                         category === "Projects" ? "🚀 Projects" :
                         category === "Support" ? "🆘 Support" :
                         category === "Showcase" ? "🌟 Showcase" : category}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Results Count */}
            <div className="text-sm text-base-content/60">
              Showing <span className="font-semibold text-primary">{getFilteredAndSortedPosts().length}</span> of <span className="font-semibold">{posts.length}</span> posts
            </div>
          </div>

          {/* Bottom Row - Filter Buttons */}
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setFilterBy("all")}
                className={`btn btn-sm rounded-full transition-all duration-300 ${
                  filterBy === "all"
                    ? "bg-primary/20 border-primary/40 text-primary"
                    : "btn-outline hover:bg-primary/10 border-primary/20 hover:border-primary/40 text-primary"
                }`}
              >
                📋 All Posts
              </button>
              <button
                onClick={() => setFilterBy("popular")}
                className={`btn btn-sm rounded-full transition-all duration-300 ${
                  filterBy === "popular"
                    ? "bg-accent/20 border-accent/40 text-accent"
                    : "btn-outline hover:bg-accent/10 border-accent/20 hover:border-accent/40 text-accent"
                }`}
              >
                🔥 Popular
              </button>
              <button
                onClick={() => setFilterBy("trending")}
                className={`btn btn-sm rounded-full transition-all duration-300 ${
                  filterBy === "trending"
                    ? "bg-secondary/20 border-secondary/40 text-secondary"
                    : "btn-outline hover:bg-secondary/10 border-secondary/20 hover:border-secondary/40 text-secondary"
                }`}
              >
                📈 Trending
              </button>
            </div>

            {/* Enhanced Search */}
            <div className="flex items-center space-x-3">
              <div className="form-control">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search posts, authors, tags..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="input input-sm input-bordered rounded-full w-full max-w-xs bg-base-100/50 border-primary/20 focus:border-primary/40 transition-all duration-300 pl-10"
                  />
                  <svg className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery("")}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-base-content/40 hover:text-base-content transition-colors"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>

              {/* Clear All Filters */}
              {(searchQuery || selectedCategory !== "all" || filterBy !== "all") && (
                <button
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedCategory("all");
                    setFilterBy("all");
                  }}
                  className="btn btn-sm btn-ghost rounded-full text-base-content/60 hover:text-base-content"
                >
                  Clear All
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Posts List */}
      {getFilteredAndSortedPosts().length === 0 ? (
        <div className="card bg-base-100 shadow-xl border border-base-300">
          <div className="card-body text-center py-16">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold mb-2">No posts found</h3>
            <p className="text-base-content/60 mb-6">
              {searchQuery ? `No posts match "${searchQuery}"` : "No posts match your current filters"}
            </p>
            <button
              onClick={() => {
                setSearchQuery("");
                setSelectedCategory("all");
                setFilterBy("all");
              }}
              className="btn btn-primary rounded-lg"
            >
              Clear Filters
            </button>
          </div>
        </div>
      ) : loading ? (
        <div className="card bg-base-100 shadow-xl border border-base-300">
          <div className="card-body text-center py-16">
            <div className="loading loading-spinner loading-lg text-primary"></div>
            <p className="text-base-content/60 mt-4">Loading posts...</p>
          </div>
        </div>
      ) : error ? (
        <div className="card bg-base-100 shadow-xl border border-error/20">
          <div className="card-body text-center py-16">
            <div className="text-6xl mb-4">⚠️</div>
            <h3 className="text-2xl font-bold mb-2 text-error">Error Loading Posts</h3>
            <p className="text-base-content/60 mb-6">{error}</p>
            <button
              onClick={() => fetchPosts()}
              className="btn btn-primary rounded-lg"
            >
              Try Again
            </button>
          </div>
        </div>
      ) : viewMode === "card" ? (
        <div className="grid gap-6 justify-center md:grid-cols-2 xl:grid-cols-3 auto-rows-fr">
          {getFilteredAndSortedPosts().map(post => (
            <div key={post.id} className="group card h-full w-full max-w-[512px] mx-auto bg-gradient-to-br from-base-100 to-base-200 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-1 border border-primary/5 hover:border-primary/20 flex flex-col">
              <div className="card-body p-8 flex flex-col">
                {/* Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center space-x-4 flex-wrap">
                    <div className="avatar">
                      <div className="w-12 h-12 rounded-full">
                        <Image
                          src={post.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(post.author.name)}&background=random`}
                          alt={post.author.name}
                          width={48}
                          height={48}
                          className="rounded-full w-12 h-12 object-cover"
                        />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center space-x-3 flex-wrap">
                        <span className="font-bold text-lg">{post.author.name}</span>
                        <span className={`badge ${getRoleBadgeColor(post.author.role)} badge-sm`}>
                          {post.author.role}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 mt-1 text-sm text-base-content/60">
                        <span>{formatTimeAgo(post.createdAt)}</span>
                        <span className="w-1 h-1 bg-base-content/30 rounded-full"></span>
                        <span>{Math.floor((post.content?.length || 0) / 200) + 1} min read</span>
                      </div>
                    </div>
                  </div>
                  {/* Post Actions Dropdown */}
                  <div className="dropdown dropdown-end">
                    <div tabIndex={0} role="button" className="btn btn-ghost btn-sm btn-circle hover:bg-primary/10 transition-colors duration-300">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </div>
                    <ul tabIndex={0} className="dropdown-content z-[50] menu p-2 shadow-2xl bg-base-100 rounded-2xl w-48 border border-primary/10">
                      <li><button onClick={() => handleShare(post.id)} className="rounded-xl hover:bg-primary/10 w-full text-left">📤 Share Post</button></li>
                      <li><button onClick={() => handleShare(post.id)} className="rounded-xl hover:bg-primary/10 w-full text-left">🔗 Copy Link</button></li>
                      <li><button onClick={() => console.log(`Saved post ${post.id}`)} className="rounded-xl hover:bg-primary/10 w-full text-left">🔖 Save Post</button></li>
                      <div className="divider my-1"></div>
                      <li><button onClick={() => console.log(`Reported post ${post.id}`)} className="rounded-xl hover:bg-error/10 text-error w-full text-left">🚨 Report</button></li>
                    </ul>
                  </div>
                </div>
                {/* Post Content */}
                <div className="mb-6">
                  <Link href={`/posts/${post.id}`} className="block group-hover:text-primary transition-colors duration-300">
                    <h2 className="text-2xl font-bold mb-3 leading-tight hover:text-primary transition-colors">
                      {post.title}
                    </h2>
                  </Link>
                  <p className="text-base-content/80 leading-relaxed line-clamp-3 text-lg">
                    {post.content}
                  </p>
                  {(post.images?.length > 0 || post.videoUrl) && (
                    <div className="flex items-center space-x-3 mt-3">
                      {post.images?.length > 0 && (
                        <div className="flex items-center space-x-1 text-accent">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span className="text-sm">{post.images.length} image{post.images.length > 1 ? 's' : ''}</span>
                        </div>
                      )}
                      {post.videoUrl && (
                        <div className="flex items-center space-x-1 text-secondary">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          <span className="text-sm">Video</span>
                        </div>
                      )}
                    </div>
                  )}
                  {post.community && (
                    <div className="mt-3">
                      <Link href={`/communities/${post.community.slug}`} className="badge badge-primary badge-sm hover:badge-outline transition-colors">
                        {post.community.name}
                      </Link>
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2 mt-3">
                    <span className="badge badge-outline badge-sm hover:badge-primary cursor-pointer transition-colors">
                      #{post.published ? 'published' : 'draft'}
                    </span>
                  </div>
                </div>
                {/* Action Bar */}
                <div className="mt-auto">
                  <div className="flex items-center justify-between pt-4 border-t border-base-300">
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => handleLike(post.id)}
                        className={`flex items-center space-x-2 transition-colors duration-300 group/like ${likedPosts.has(post.id) ? 'text-error' : 'hover:text-error'}`}
                      >
                        <div className="p-2 rounded-full group-hover/like:bg-error/10 transition-colors duration-300">
                          <svg className={`w-5 h-5 ${likedPosts.has(post.id) ? 'fill-current' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                        </div>
                        <span className="font-medium">{post._count.likes}</span>
                      </button>
                      <Link href={`/posts/${post.id}#comments`} className="flex items-center space-x-2 hover:text-info transition-colors duration-300 group/comment">
                        <div className="p-2 rounded-full group-hover/comment:bg-info/10 transition-colors duration-300">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                        </div>
                        <span className="font-medium">{post._count.comments}</span>
                      </Link>
                      <button onClick={() => handleShare(post.id)} className="flex items-center space-x-2 hover:text-secondary transition-colors duration-300 group/share">
                        <div className="p-2 rounded-full group-hover/share:bg-secondary/10 transition-colors duration-300">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                          </svg>
                        </div>
                        <span className="font-medium">Share</span>
                      </button>
                      <div className="flex items-center space-x-2 text-base-content/60">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <span className="text-sm">{viewCounts[post.id] ?? 0}</span>
                      </div>
                    </div>
                    <div className="pt-4">
                      <Link href={`/posts/${post.id}`} className="btn btn-primary btn-sm rounded-full px-6 hover:shadow-lg transition-all duration-300 hover:scale-105 group/read">
                        <span className="flex items-center">
                          Read More
                          <svg className="w-4 h-4 ml-1 group-hover/read:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                        </span>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {getFilteredAndSortedPosts().map(post => (
            <div key={post.id} className="group card card-side bg-gradient-to-br from-base-100 to-base-200 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-1 border border-primary/5 hover:border-primary/20">
              <div className="card-body p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="avatar">
                      <div className="w-12 h-12 rounded-full">
                        <Image
                          src={post.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(post.author.name)}&background=random`}
                          alt={post.author.name}
                          width={48}
                          height={48}
                          className="rounded-full w-12 h-12 object-cover"
                        />
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center space-x-3 flex-wrap">
                        <span className="font-bold text-lg">{post.author.name}</span>
                        <span className={`badge ${getRoleBadgeColor(post.author.role)} badge-sm`}>
                          {post.author.role}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 mt-1 text-sm text-base-content/60">
                        <span>{formatTimeAgo(post.createdAt)}</span>
                        <span className="w-1 h-1 bg-base-content/30 rounded-full"></span>
                        <span>{Math.floor((post.content?.length || 0) / 200) + 1} min read</span>
                      </div>
                    </div>
                  </div>
                  <div className="dropdown dropdown-end">
                    <div tabIndex={0} role="button" className="btn btn-ghost btn-sm btn-circle hover:bg-primary/10 transition-colors duration-300">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </div>
                    <ul tabIndex={0} className="dropdown-content z-[50] menu p-2 shadow-2xl bg-base-100 rounded-2xl w-48 border border-primary/10">
                      <li><button onClick={() => handleShare(post.id)} className="rounded-xl hover:bg-primary/10 w-full text-left">📤 Share Post</button></li>
                      <li><button onClick={() => handleShare(post.id)} className="rounded-xl hover:bg-primary/10 w-full text-left">🔗 Copy Link</button></li>
                      <li><button onClick={() => console.log(`Saved post ${post.id}`)} className="rounded-xl hover:bg-primary/10 w-full text-left">🔖 Save Post</button></li>
                      <div className="divider my-1"></div>
                      <li><button onClick={() => console.log(`Reported post ${post.id}`)} className="rounded-xl hover:bg-error/10 text-error w-full text-left">🚨 Report</button></li>
                    </ul>
                  </div>
                </div>
                <div className="mb-4">
                  <Link href={`/posts/${post.id}`} className="block group-hover:text-primary transition-colors duration-300">
                    <h2 className="text-xl font-bold mb-3 leading-tight hover:text-primary transition-colors">
                      {post.title}
                    </h2>
                  </Link>
                  <p className="text-base-content/80 leading-relaxed line-clamp-2">
                    {post.content}
                  </p>
                  {(post.images?.length > 0 || post.videoUrl) && (
                    <div className="flex items-center space-x-3 mt-3">
                      {post.images?.length > 0 && (
                        <div className="flex items-center space-x-1 text-accent">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span className="text-sm">{post.images.length} image{post.images.length > 1 ? 's' : ''}</span>
                        </div>
                      )}
                      {post.videoUrl && (
                        <div className="flex items-center space-x-1 text-secondary">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                          <span className="text-sm">Video</span>
                        </div>
                      )}
                    </div>
                  )}
                  {post.community && (
                    <div className="mt-3">
                      <Link href={`/communities/${post.community.slug}`} className="badge badge-primary badge-sm hover:badge-outline transition-colors">
                        {post.community.name}
                      </Link>
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2 mt-3">
                    <span className="badge badge-outline badge-sm hover:badge-primary cursor-pointer transition-colors">
                      #{post.published ? 'published' : 'draft'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between pt-4 border-t border-base-300">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => handleLike(post.id)}
                      className={`flex items-center space-x-2 transition-colors duration-300 group/like ${likedPosts.has(post.id) ? 'text-error' : 'hover:text-error'}`}
                    >
                      <div className="p-2 rounded-full group-hover/like:bg-error/10 transition-colors duration-300">
                        <svg className={`w-5 h-5 ${likedPosts.has(post.id) ? 'fill-current' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      </div>
                      <span className="font-medium">{post._count.likes}</span>
                    </button>
                    <Link href={`/posts/${post.id}#comments`} className="flex items-center space-x-2 hover:text-info transition-colors duration-300 group/comment">
                      <div className="p-2 rounded-full group-hover/comment:bg-info/10 transition-colors duration-300">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                      </div>
                      <span className="font-medium">{post._count.comments}</span>
                    </Link>
                    <button onClick={() => handleShare(post.id)} className="flex items-center space-x-2 hover:text-secondary transition-colors duration-300 group/share">
                      <div className="p-2 rounded-full group-hover/share:bg-secondary/10 transition-colors duration-300">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                      </div>
                      <span className="font-medium">Share</span>
                    </button>
                    <div className="flex items-center space-x-2 text-base-content/60">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span className="text-sm">{viewCounts[post.id] ?? 0}</span>
                    </div>
                  </div>
                  <Link href={`/posts/${post.id}`} className="btn btn-primary btn-sm rounded-full px-6 hover:shadow-lg transition-all duration-300 hover:scale-105 group/read">
                    Read More
                    <svg className="w-4 h-4 ml-1 group-hover/read:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Enhanced Pagination & Stats */}
      {getFilteredAndSortedPosts().length > 0 && (
        <div className="card bg-gradient-to-r from-base-200/50 to-base-300/30 shadow-xl border border-primary/10">
          <div className="card-body p-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-base-content/70">
                Showing <span className="font-semibold text-primary">{getFilteredAndSortedPosts().length}</span> of <span className="font-semibold text-primary">{posts.length}</span> posts
                {searchQuery && (
                  <span className="ml-2">
                    for &quot;<span className="font-semibold text-accent">{searchQuery}</span>&quot;
                  </span>
                )}
              </div>

              {/* Pagination Controls */}
              <div className="join shadow-lg">
                <button className="join-item btn btn-outline hover:bg-primary/10 border-primary/20 hover:border-primary/40 transition-all duration-300" disabled>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                  </svg>
                  Previous
                </button>
                <button className="join-item btn bg-primary/20 border-primary/40 text-primary hover:bg-primary/30 transition-all duration-300">1</button>
                <button className="join-item btn btn-outline hover:bg-primary/10 border-primary/20 hover:border-primary/40 transition-all duration-300">2</button>
                <button className="join-item btn btn-outline hover:bg-primary/10 border-primary/20 hover:border-primary/40 transition-all duration-300">3</button>
                <button className="join-item btn btn-outline hover:bg-primary/10 border-primary/20 hover:border-primary/40 transition-all duration-300">
                  Next
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center gap-3">
                <div className="text-sm text-base-content/70">
                  Jump to:
                  <select className="select select-sm select-bordered ml-2 bg-base-100/50 border-primary/20 focus:border-primary/40 rounded-full">
                    <option>Page 1</option>
                    <option>Page 2</option>
                    <option>Page 3</option>
                  </select>
                </div>

                {session?.user && (
                  <Link href="/posts/create" className="btn btn-sm btn-primary rounded-full">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                    </svg>
                    New Post
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
