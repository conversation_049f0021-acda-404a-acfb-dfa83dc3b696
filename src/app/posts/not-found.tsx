import Link from 'next/link';

export default function PostsNotFound() {
  return (
    <main className="min-h-dvh flex flex-col items-center justify-center px-6 py-12 text-center gap-6">
      <div>
        <h1 className="text-6xl font-extrabold tracking-tight">
          <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">No Post</span>
        </h1>
        <p className="mt-4 text-xl font-semibold">Post Not Found</p>
        <p className="mt-3 opacity-70 max-w-md mx-auto">
          The post you&apos;re looking for doesn&apos;t exist, was removed, or is private.
        </p>
      </div>
      <div className="flex gap-4">
        <Link href="/posts/create" className="btn btn-primary">Create Post</Link>
        <Link href="/" className="btn btn-outline">Home</Link>
      </div>
    </main>
  );
}
