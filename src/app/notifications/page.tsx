"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import Link from "next/link";

// Types
interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  actionUrl?: string;
  read: boolean;
  createdAt: string;
  fromUser?: {
    id: string;
    name: string;
    image: string | null;
  };
  post?: {
    id: string;
    title: string;
  };
  comment?: {
    id: string;
    content: string;
    post: {
      id: string;
      title: string;
    };
  };
}

interface NotificationsResponse {
  notifications: Notification[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  unreadCount: number;
}

export default function NotificationsPage() {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<"all" | "unread" | "likes" | "comments" | "follows">("all");
  const [unreadCount, setUnreadCount] = useState(0);

  // Fetch notifications from API
  useEffect(() => {
    const fetchNotifications = async () => {
      if (!session?.user) return;

      try {
        setLoading(true);
        const filterParam = filter !== 'all' ? `&filter=${filter}` : '';
        const response = await fetch(`/api/notifications?limit=50${filterParam}`);
        
        if (response.ok) {
          const data: NotificationsResponse = await response.json();
          setNotifications(data.notifications);
          setUnreadCount(data.unreadCount);
        } else {
          console.error('Failed to fetch notifications');
          setNotifications([]);
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
        setNotifications([]);
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, [session, filter]);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return "1 day ago";
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks === 1) return "1 week ago";
    return `${diffInWeeks} weeks ago`;
  };

  const getNotificationIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'like':
        return "❤️";
      case 'comment':
        return "💬";
      case 'follow':
        return "👤";
      case 'mention':
        return "@";
      case 'post':
        return "📝";
      case 'community':
        return "🏠";
      default:
        return "🔔";
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case "unread":
        return !notification.read;
      case "likes":
        return notification.type === "like";
      case "comments":
        return notification.type === "comment" || notification.type === "mention";
      case "follows":
        return notification.type === "follow";
      default:
        return true;
    }
  });

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notificationIds: [notificationId] })
      });

      if (response.ok) {
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === notificationId
              ? { ...notification, read: true }
              : notification
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ markAllAsRead: true })
      });

      if (response.ok) {
        setNotifications(prev =>
          prev.map(notification => ({ ...notification, read: true }))
        );
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    );
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30 flex items-center justify-center">
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10 w-full max-w-md">
          <div className="card-body text-center py-12">
            <div className="text-6xl mb-4">🔔</div>
            <h2 className="text-2xl font-bold mb-2">Sign In Required</h2>
            <p className="text-base-content/70 mb-6">
              Please sign in to view your notifications
            </p>
            <Link href="/auth/signin" className="btn btn-primary btn-lg rounded-full px-8">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              Sign In
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
      <div className="max-w-4xl mx-auto space-y-8 p-4">
        {/* Header */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body p-8">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-2">
                  Notifications
                </h1>
                <p className="text-base-content/70 text-lg">
                  Stay updated with your latest activity
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="stats shadow-lg bg-gradient-to-r from-primary/5 to-accent/5">
                  <div className="stat">
                    <div className="stat-title text-xs">Total</div>
                    <div className="stat-value text-2xl text-primary">{notifications.length}</div>
                  </div>
                  <div className="stat">
                    <div className="stat-title text-xs">Unread</div>
                    <div className="stat-value text-2xl text-accent">{unreadCount}</div>
                  </div>
                </div>
                {unreadCount > 0 && (
                  <button 
                    onClick={markAllAsRead}
                    className="btn btn-outline btn-primary rounded-full px-6"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Mark All Read
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Notifications List */}
        <div className="space-y-4">
          {filteredNotifications.length === 0 ? (
            <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
              <div className="card-body text-center py-16">
                <div className="text-6xl mb-4">
                  {filter === "unread" ? "✅" : "🔔"}
                </div>
                <h3 className="text-2xl font-bold mb-2">
                  {filter === "unread" ? "All caught up!" : "No notifications"}
                </h3>
                <p className="text-base-content/70">
                  {filter === "unread"
                    ? "You have no unread notifications"
                    : "You don't have any notifications yet"}
                </p>
              </div>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border transition-all duration-300 hover:shadow-2xl hover:-translate-y-1 ${
                  notification.read
                    ? "border-base-300/50"
                    : "border-primary/20 bg-gradient-to-br from-primary/5 to-accent/5"
                }`}
              >
                <div className="card-body p-6">
                  <div className="flex items-start space-x-4">
                    {/* Avatar/Icon */}
                    <div className="flex-shrink-0">
                      {notification.fromUser?.image ? (
                        <div className="avatar">
                          <div className="w-12 h-12 rounded-full ring-2 ring-primary/20">
                            <img src={notification.fromUser.image} alt="User avatar" />
                          </div>
                        </div>
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center text-2xl">
                          {getNotificationIcon(notification.type)}
                        </div>
                      )}
                      {!notification.read && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full"></div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-base text-base-content">
                          {notification.title}
                        </h3>
                        <div className="flex items-center space-x-2 ml-4">
                          <span className="text-xs text-base-content/60 whitespace-nowrap">
                            {formatTimeAgo(notification.createdAt)}
                          </span>
                          <div className="dropdown dropdown-end">
                            <div tabIndex={0} role="button" className="btn btn-ghost btn-xs btn-circle">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01" />
                              </svg>
                            </div>
                            <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-52">
                              {!notification.read && (
                                <li>
                                  <button onClick={() => markAsRead(notification.id)}>
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    Mark as read
                                  </button>
                                </li>
                              )}
                              <li>
                                <button
                                  onClick={() => deleteNotification(notification.id)}
                                  className="text-error"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                  Delete
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <p className="text-base-content/70 text-sm mb-3 line-clamp-2">
                        {notification.message}
                      </p>

                      {/* Action Button */}
                      {notification.actionUrl && (
                        <Link
                          href={notification.actionUrl}
                          className="btn btn-ghost btn-sm rounded-full hover:btn-primary hover:btn-outline"
                          onClick={() => !notification.read && markAsRead(notification.id)}
                        >
                          View
                          <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Settings */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
          <div className="card-body p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-bold mb-1">Notification Settings</h3>
                <p className="text-base-content/70 text-sm">
                  Customize how you receive notifications
                </p>
              </div>
              <Link href="/settings/notifications" className="btn btn-outline btn-primary rounded-full px-6">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Settings
              </Link>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
          <div className="card-body p-6">
            <div className="flex flex-wrap gap-2">
              {[
                { key: "all", label: "All", icon: "📋" },
                { key: "unread", label: "Unread", icon: "🔴" },
                { key: "likes", label: "Likes", icon: "❤️" },
                { key: "comments", label: "Comments", icon: "💬" },
                { key: "follows", label: "Follows", icon: "👤" }
              ].map(({ key, label, icon }) => (
                <button
                  key={key}
                  onClick={() => setFilter(key as any)}
                  className={`btn btn-sm rounded-full ${
                    filter === key 
                      ? "btn-primary" 
                      : "btn-ghost hover:btn-primary hover:btn-outline"
                  }`}
                >
                  <span className="mr-1">{icon}</span>
                  {label}
                  {key === "unread" && unreadCount > 0 && (
                    <span className="badge badge-error badge-xs ml-1">{unreadCount}</span>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
