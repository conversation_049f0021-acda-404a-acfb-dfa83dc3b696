"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import Link from "next/link";
import UserCard from "@/components/UserCard";
import { useFollowing } from "@/hooks/useFollowing";


export default function FollowingPage() {
  const { data: session } = useSession();
  const { followingData, toggleFollow } = useFollowing();
  const [following, setFollowing] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"recent" | "name" | "followers" | "activity">("recent");
  const [filterBy, setFilterBy] = useState<"all" | "online" | "active">("all");

  // Load real following data from API
  useEffect(() => {
    const fetchFollowing = async () => {
      try {
        const res = await fetch('/api/following');
        if (!res.ok) throw new Error('Failed to fetch following');
        const data = await res.json();
        // Map API data to expected shape
        setFollowing((data.users || []).map((user: any) => ({
          id: user.id,
          name: user.name || 'Anonymous User',
          username: user.name?.toLowerCase().replace(/\s+/g, '_') || 'anonymous',
          email: user.email || '',
          avatar: user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name || 'User')}&background=06b6d4&color=fff`,
          bio: user.bio || 'No bio available',
          postCount: user._count?.posts || 0,
          followerCount: user._count?.followers || 0,
          followingCount: user._count?.following || 0,
          isFollowing: true,
          joinedDate: new Date(user.createdAt),
          followedAt: user.followedAt ? new Date(user.followedAt) : new Date(),
        })));
      } catch (err) {
        setFollowing([]);
      }
    };
    fetchFollowing();
  }, [session]);

  const handleFollowChange = async (userId: string, isFollowing: boolean) => {
    await toggleFollow(userId);
    // Refetch following list after change
    const res = await fetch('/api/following');
    if (res.ok) {
      const data = await res.json();
      setFollowing((data.users || []).map((user: any) => ({
        id: user.id,
        name: user.name || 'Anonymous User',
        username: user.name?.toLowerCase().replace(/\s+/g, '_') || 'anonymous',
        email: user.email || '',
        avatar: user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name || 'User')}&background=06b6d4&color=fff`,
        bio: user.bio || 'No bio available',
        postCount: user._count?.posts || 0,
        followerCount: user._count?.followers || 0,
        followingCount: user._count?.following || 0,
        isOnline: false, // Not available from API
        isFollowing: true,
        joinedDate: new Date(user.createdAt),
        followedAt: user.followedAt ? new Date(user.followedAt) : new Date(),
      })));
    }
  };

  const filteredFollowing = following
    .filter(user => {
      const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           user.bio.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesFilter = filterBy === "all" || 
                           (filterBy === "online" && user.isOnline) ||
                           (filterBy === "active" && (user.postCount || 0) > 50);
      
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "followers":
          return (b.followerCount || 0) - (a.followerCount || 0);
        case "activity":
          return (b.postCount || 0) - (a.postCount || 0);
        case "recent":
        default:
          return b.followedAt.getTime() - a.followedAt.getTime();
      }
    });

  const formatFollowDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short',
      day: 'numeric'
    });
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30 flex items-center justify-center">
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body text-center p-12">
            <h2 className="text-2xl font-bold mb-4">Sign In Required</h2>
            <p className="text-base-content/70 mb-6">Please sign in to view who you're following.</p>
            <Link href="/auth/signin" className="btn btn-primary rounded-full">
              Sign In
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
      <div className="max-w-7xl mx-auto space-y-8 p-4">
        {/* Header */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body p-8">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-2">
                  Following
                </h1>
                <p className="text-base-content/70 text-lg">
                  People you're following
                </p>
              </div>
              <div className="stats shadow-lg bg-gradient-to-r from-primary/5 to-accent/5">
                <div className="stat">
                  <div className="stat-title text-xs">Following</div>
                  <div className="stat-value text-2xl text-primary">{following.length || 0}</div>
                </div>
                <div className="stat">
                  <div className="stat-title text-xs">Active Users</div>
                  <div className="stat-value text-2xl text-info">
                    {following.filter(u => (u.postCount || 0) > 50).length || 0}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
          <div className="card-body p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="form-control">
                  <div className="input-group">
                    <span className="bg-base-200">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </span>
                    <input
                      type="text"
                      placeholder="Search following..."
                      className="input input-bordered w-full"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Sort */}
              <div className="form-control">
                <select 
                  className="select select-bordered"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                >
                  <option value="recent">Recently Followed</option>
                  <option value="name">Name (A-Z)</option>
                  <option value="followers">Most Followers</option>
                  <option value="activity">Most Active</option>
                </select>
              </div>

              {/* Filter */}
              <div className="form-control">
                <select 
                  className="select select-bordered"
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value as any)}
                >
                  <option value="all">All Following</option>
                  <option value="online">Online Now</option>
                  <option value="active">Active Users</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-4">
          <Link href="/users" className="btn btn-outline btn-primary rounded-full">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Find More Users
          </Link>
          <Link href="/followers" className="btn btn-ghost rounded-full">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            View Followers
          </Link>
        </div>

        {/* Following List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredFollowing.length === 0 ? (
            <div className="col-span-full">
              <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
                <div className="card-body text-center py-16">
                  <div className="text-6xl mb-4">👤</div>
                  <h3 className="text-2xl font-bold mb-2">No users found</h3>
                  <p className="text-base-content/70 mb-6">
                    {searchQuery ? "Try adjusting your search or filters." : "Start following users to see them here!"}
                  </p>
                  <Link href="/users" className="btn btn-primary rounded-full">
                    Discover Users
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            filteredFollowing.map((user) => (
              <div key={user.id} className="relative">
                <UserCard
                  user={user}
                  onFollowChange={handleFollowChange}
                  variant="detailed"
                  showFollowButton={true}
                />
                <div className="absolute top-2 right-2">
                  <div className="tooltip tooltip-left" data-tip={`Followed on ${formatFollowDate(user.followedAt)}`}>
                    <div className="badge badge-accent badge-xs">
                      {formatFollowDate(user.followedAt)}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
