"use client";

import Link from "next/link";
import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import LogoIntro from "@/components/LogoIntro";

// Types
interface Post {
  id: string;
  title: string;
  content: string;
  images: string[];
  videoUrl: string | null;
  createdAt: string;
  author: {
    id: string;
    name: string;
    role: string;
    image: string;
  };
  community?: {
    id: string;
    name: string;
    slug: string;
  };
  _count: {
    comments: number;
    likes: number;
  };
}

interface Community {
  id: string;
  name: string;
  description: string;
  slug: string;
  image: string | null;
  _count: {
    members: number;
    posts: number;
  };
}

export default function Home() {
  const { data: session, status } = useSession();
  const [recentPosts, setRecentPosts] = useState<Post[]>([]);
  const [topCommunities, setTopCommunities] = useState<Community[]>([]);
  const [loading, setLoading] = useState(true);
  const [userCount, setUserCount] = useState<number | null>(null);
  const [communityCount, setCommunityCount] = useState<number | null>(null);
  const [postCount, setPostCount] = useState<number | null>(null);

  // Fetch recent posts and communities
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [postsRes, communitiesRes, statsRes] = await Promise.all([
          fetch('/api/posts?published=true&limit=3'),
          fetch('/api/communities?limit=4'),
          fetch('/api/stats/summary')
        ]);

        if (postsRes.ok) {
          const postsData = await postsRes.json();
          setRecentPosts(postsData.posts || []);
        }

        if (communitiesRes.ok) {
          const communitiesData = await communitiesRes.json();
          setTopCommunities(communitiesData.communities || []);
        }

        if (statsRes.ok) {
          const statsData = await statsRes.json();
          if (typeof statsData.userCount === 'number') setUserCount(statsData.userCount);
          if (typeof statsData.communityCount === 'number') setCommunityCount(statsData.communityCount);
          if (typeof statsData.postCount === 'number') setPostCount(statsData.postCount);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return "Less than an hour ago";
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "ADMIN": return "badge-error";
      case "EDITOR": return "badge-warning";
      default: return "badge-info";
    }
  };
  return (
    <div className="content-spacing">
      {/* Hero Section */}
      <div className="hero min-h-[60vh] bg-gradient-to-br from-base-200 via-base-300 to-base-200 rounded-3xl shadow-2xl overflow-hidden relative section-spacing">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-accent/5" />
        <div className="hero-content w-full p-0 relative z-10 flex flex-col items-center">
          {/* Video centered with minimal surrounding spacing */}
          <div className="w-full flex justify-center pt-6">
            <LogoIntro
              videoSrc="/video/logo.mkv"
              logoSrc="/images/logo.png"
              alt="COMFOR Logo"
              heightPx={500}
              fullWidth
              maxVideoWidthPx={1800}
              viewportWidthPct={95}
            />
          </div>
          {/* Text + CTA block */}
          <div className="w-full max-w-3xl px-4 pb-10 text-center">
            <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium mt-4 mb-5 backdrop-blur-sm">
              <span className="w-2 h-2 bg-primary rounded-full mr-2 animate-pulse" />
              Now Live
            </div>
            <p className="text-lg text-primary/80 font-semibold tracking-wider mb-3">Welcome to COMFOR</p>
            <div className="h-1 w-24 bg-gradient-to-r from-primary to-accent mx-auto mb-8 rounded-full" />
            <p className="text-xl text-base-content/80 leading-relaxed mb-8 mx-auto max-w-2xl">
              Experience the future of community driven discussions with our modern, role-based forum featuring real-time interactions and elegant design.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {session ? (
                // Authenticated user buttons
                <>
                  <Link href="/posts" className="btn btn-primary btn-lg rounded-full px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    Explore Posts
                  </Link>
                  <Link href="/communities" className="btn btn-outline btn-lg rounded-full px-8 hover:bg-primary/10 transition-all duration-300">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Browse Communities
                  </Link>
                </>
              ) : (
                // Non-authenticated user buttons
                <>
                  <Link href="/auth/register" className="btn btn-primary btn-lg rounded-full px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                    Join Com4
                  </Link>
                  <Link href="/auth/signin" className="btn btn-outline btn-lg rounded-full px-8 hover:bg-primary/10 transition-all duration-300">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Sign In
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 grid-spacing section-spacing">
        <div className="group enhanced-card hover:shadow-3xl hover:-translate-y-2">
          <div className="enhanced-card-body">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-primary/10 rounded-2xl group-hover:bg-primary/20 transition-colors duration-300">
                <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="badge badge-primary badge-outline">Security</div>
            </div>
            <h2 className="text-2xl font-bold mb-3 group-hover:text-primary transition-colors duration-300">
              Role-Based Access
            </h2>
            <p className="text-base-content/70 leading-relaxed">
              Sophisticated permission system with Admin, Editor, and User roles for secure content management
            </p>
          </div>
        </div>

        <div className="group enhanced-card hover:shadow-3xl hover:-translate-y-2">
          <div className="enhanced-card-body">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-accent/10 rounded-2xl group-hover:bg-accent/20 transition-colors duration-300">
                <svg className="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="badge badge-accent badge-outline">Interactive</div>
            </div>
            <h2 className="text-2xl font-bold mb-3 group-hover:text-accent transition-colors duration-300">
              Smart Discussions
            </h2>
            <p className="text-base-content/70 leading-relaxed">
              Nested comment threads with real-time updates and intelligent conversation flow
            </p>
          </div>
        </div>

        <div className="group enhanced-card hover:shadow-3xl hover:-translate-y-2">
          <div className="enhanced-card-body">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-secondary/10 rounded-2xl group-hover:bg-secondary/20 transition-colors duration-300">
                <svg className="w-8 h-8 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <div className="badge badge-secondary badge-outline">Social</div>
            </div>
            <h2 className="text-2xl font-bold mb-3 group-hover:text-secondary transition-colors duration-300">
              Engagement Hub
            </h2>
            <p className="text-base-content/70 leading-relaxed">
              Advanced like system and seamless social sharing with rich preview generation
            </p>
          </div>
        </div>
      </div>

      {/* Recent Posts Preview */}
      <div className="enhanced-card section-spacing">
        <div className="enhanced-card-body">
          <div className="flex items-center justify-between page-header">
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Trending Discussions
              </h2>
              <p className="text-base-content/60 mt-2">Join the conversation with our community</p>
            </div>
            <div className="hidden sm:flex items-center space-x-2">
              <div className="w-3 h-3 bg-success rounded-full animate-pulse"></div>
              <span className="text-sm text-success font-medium">Live</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading ? (
              [...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse p-6 bg-base-200/50 rounded-2xl">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-base-300 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-base-300 rounded w-1/4"></div>
                      <div className="h-6 bg-base-300 rounded w-3/4"></div>
                      <div className="h-4 bg-base-300 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))
            ) : recentPosts.length > 0 ? (
              recentPosts.map((post, index) => (
                <div key={post.id} className={`group p-6 bg-gradient-to-r from-base-200/50 to-base-300/30 rounded-2xl border transition-all duration-300 hover:shadow-lg ${
                  index === 0 ? 'border-primary/10 hover:border-primary/30' :
                  index === 1 ? 'border-accent/10 hover:border-accent/30' :
                  'border-secondary/10 hover:border-secondary/30'
                }`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="avatar">
                          <div className="w-10 h-10 rounded-full">
                            <img
                              src={post.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(post.author.name)}&background=random`}
                              alt={post.author.name}
                              className="rounded-full"
                            />
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-semibold text-sm">{post.author.name}</span>
                            <span className={`badge ${getRoleBadgeColor(post.author.role)} badge-xs`}>
                              {post.author.role}
                            </span>
                          </div>
                          <span className="text-xs text-base-content/50">{formatTimeAgo(post.createdAt)}</span>
                        </div>
                      </div>
                      <Link href={`/posts/${post.id}`}>
                        <h3 className={`font-bold text-lg mb-2 transition-colors duration-300 hover:underline ${
                          index === 0 ? 'group-hover:text-primary' :
                          index === 1 ? 'group-hover:text-accent' :
                          'group-hover:text-secondary'
                        }`}>
                          {post.title}
                        </h3>
                      </Link>
                      <p className="text-base-content/70 text-sm line-clamp-2">
                        {post.content.replace(/[#*`]/g, '').substring(0, 100)}...
                      </p>

                      {/* Media Indicators */}
                      {(post.images?.length > 0 || post.videoUrl) && (
                        <div className="flex items-center space-x-2 mt-2">
                          {post.images?.length > 0 && (
                            <div className="flex items-center space-x-1 text-accent">
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <span className="text-xs">{post.images.length}</span>
                            </div>
                          )}
                          {post.videoUrl && (
                            <div className="flex items-center space-x-1 text-secondary">
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Community Badge */}
                      {post.community && (
                        <div className="mt-2">
                          <Link href={`/communities/${post.community.slug}`} className="badge badge-primary badge-xs hover:badge-outline transition-colors">
                            {post.community.name}
                          </Link>
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col items-end space-y-2 ml-4">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1 text-error">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                          </svg>
                          <span className="text-sm font-medium">{post._count.likes}</span>
                        </div>
                        <div className="flex items-center space-x-1 text-info">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          <span className="text-sm font-medium">{post._count.comments}</span>
                        </div>
                      </div>
                      <div className={`badge badge-outline badge-sm ${
                        index === 0 ? 'badge-primary' :
                        index === 1 ? 'badge-accent' :
                        'badge-secondary'
                      }`}>
                        {index === 0 ? 'Latest' : index === 1 ? 'Popular' : 'Trending'}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <div className="text-6xl mb-4">📝</div>
                <h3 className="text-xl font-bold mb-2">No Posts Yet</h3>
                <p className="text-base-content/60">Be the first to start a discussion!</p>
              </div>
            )}
          </div>

          <div className="flex justify-between items-center mt-8 pt-6 border-t border-base-300">
            <div className="text-sm text-base-content/60 flex flex-wrap items-center gap-3">
              <span>
                Join <span className="font-semibold text-primary">{userCount === null ? '…' : userCount.toLocaleString()}</span>{' '}
                {userCount === 1 ? 'member' : 'members'}
              </span>
              <span className="hidden sm:inline h-3 w-px bg-base-300" />
              <span>
                <span className="font-semibold text-accent">{communityCount === null ? '…' : communityCount.toLocaleString()}</span>{' '}
                {communityCount === 1 ? 'community' : 'communities'}
              </span>
              <span className="hidden sm:inline h-3 w-px bg-base-300" />
              <span>
                <span className="font-semibold text-secondary">{postCount === null ? '…' : postCount.toLocaleString()}</span>{' '}
                {postCount === 1 ? 'post' : 'posts'}
              </span>
            </div>
            <Link href="/posts" className="btn btn-primary rounded-full px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
              View All Posts
            </Link>
          </div>
        </div>
      </div>

      {/* Communities Section */}
      <div className="card bg-gradient-to-br from-base-100 to-base-200 shadow-2xl border border-accent/5">
        <div className="card-body p-8">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-accent to-secondary bg-clip-text text-transparent">
                Popular Communities
              </h2>
              <p className="text-base-content/60 mt-2">Discover communities that match your interests</p>
            </div>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-32 bg-base-300 rounded-2xl mb-4"></div>
                  <div className="h-4 bg-base-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-base-300 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          ) : topCommunities.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {topCommunities.map((community) => (
                <Link key={community.id} href={`/communities/${community.slug}`}>
                  <div className="group card bg-gradient-to-br from-base-200/50 to-base-300/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-accent/10 hover:border-accent/30">
                    <figure className="h-32 overflow-hidden rounded-t-2xl">
                      {community.image ? (
                        <img
                          src={community.image}
                          alt={community.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-primary/30 to-secondary/30 flex items-center justify-center border-2 border-dashed border-base-content/20">
                          <div className="text-6xl">🏘️</div>
                        </div>
                      )}
                    </figure>
                    <div className="card-body p-4">
                      <h3 className="card-title text-lg group-hover:text-accent transition-colors">
                        {community.name}
                      </h3>
                      <p className="text-base-content/70 text-sm line-clamp-2">
                        {community.description}
                      </p>
                      <div className="flex items-center justify-between mt-4">
                        <div className="flex items-center space-x-3 text-xs text-base-content/60">
                          <span className="flex items-center space-x-1">
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span>{community._count.members}</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span>{community._count.posts}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🏘️</div>
              <h3 className="text-xl font-bold mb-2">No Communities Yet</h3>
              <p className="text-base-content/60">Be the first to create a community!</p>
            </div>
          )}

          <div className="flex justify-center mt-8 pt-6 border-t border-base-300">
            <Link href="/communities" className="btn btn-accent rounded-full px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
              Explore All Communities
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
