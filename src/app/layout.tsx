import type { Metada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "@/components/providers";
import LayoutWrapper from "@/components/LayoutWrapper";

const inter = Inter({ subsets: ["latin"] });

// Rich site metadata (App Router). Adjust NEXT_PUBLIC_BASE_URL to your deployed origin.
export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"),
  title: {
    default: "COMFOR - Communities Forum",
    template: "%s | COMFOR"
  },
  description: "A modern communities forum platform with role-based access control and real-time discussions",
  keywords: ["forum", "communities", "discussion", "real-time", "COMFOR", "role-based access"],
  authors: [{ name: "COMFOR Team" }],
  creator: "COMFOR",
  publisher: "COMFOR",
  openGraph: {
    title: "COMFOR - Communities Forum",
    description: "A modern communities forum platform with role-based access control and real-time discussions",
    url: "/",
    siteName: "COMFOR",
    images: [
      {
        url: "/images/logo.png",
        width: 512,
        height: 512,
        alt: "COMFOR Logo"
      }
    ],
    locale: "en_US",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "COMFOR - Communities Forum",
    description: "A modern communities forum platform with role-based access control and real-time discussions",
    images: ["/images/logo.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-snippet": -1,
      "max-video-preview": -1,
    },
  },
  category: "forum",
  icons: {
    icon: "/favicon.png",
    shortcut: "/favicon.png",
    apple: "/favicon.png",
  },
  manifest: "/site.webmanifest",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#1e293b", // Tailwind slate-800 (adjust to match primary if desired)
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const theme = process.env.NEXT_PUBLIC_THEME || "retro";
  return (
    <html lang="en" data-theme={theme} className="scroll-smooth">
      <body className={inter.className}>
        <Providers>
          <LayoutWrapper>
            {children}
          </LayoutWrapper>
        </Providers>
      </body>
    </html>
  );
}
