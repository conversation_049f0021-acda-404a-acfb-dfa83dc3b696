"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

interface FollowingRelationship {
  userId: string;
  isFollowing: boolean;
  followerCount: number;
  followingCount: number;
}

interface UseFollowingReturn {
  followingData: Map<string, FollowingRelationship>;
  isLoading: boolean;
  followUser: (userId: string) => Promise<void>;
  unfollowUser: (userId: string) => Promise<void>;
  toggleFollow: (userId: string) => Promise<void>;
  getFollowingStatus: (userId: string) => FollowingRelationship | null;
  refreshFollowingData: () => Promise<void>;
}


export function useFollowing(): UseFollowingReturn {
  const { data: session, status } = useSession();
  const [followingData, setFollowingData] = useState<Map<string, FollowingRelationship>>(new Map());
  const [isLoading, setIsLoading] = useState(true);

  // Initialize following data
  useEffect(() => {
    if (status === "authenticated" && session) {
      loadFollowingData();
    } else if (status === "unauthenticated") {
      // User is not authenticated, set loading to false
      setIsLoading(false);
      setFollowingData(new Map());
    }
    // If status is "loading", keep isLoading as true
  }, [session, status]);

  const loadFollowingData = async () => {
    setIsLoading(true);
    try {
      const res = await fetch('/api/following');
      if (!res.ok) throw new Error('Failed to fetch following');
      const data = await res.json();
      const dataMap = new Map<string, FollowingRelationship>();
      (data.users || []).forEach((user: any) => {
        dataMap.set(user.id, {
          userId: user.id,
          isFollowing: true,
          followerCount: user._count?.followers || 0,
          followingCount: user._count?.following || 0
        });
      });
      console.log('Following data loaded:', dataMap.size, 'users');
      console.log('Following user IDs:', Array.from(dataMap.keys()));
      setFollowingData(dataMap);
    } catch (error) {
      console.error('Failed to load following data:', error);
      setFollowingData(new Map());
    } finally {
      setIsLoading(false);
    }
  };

  const followUser = async (userId: string) => {
    if (!session || status !== "authenticated") {
      console.warn('Cannot follow user: not authenticated');
      return;
    }
    try {
      const res = await fetch('/api/following', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });
      if (!res.ok) {
        const errorData = await res.json();
        console.error('Follow API error:', errorData);
        throw new Error(errorData.error || 'Failed to follow user');
      }
      
      // Update state immediately for better UX
      setFollowingData(prev => {
        const newData = new Map(prev);
        newData.set(userId, {
          userId,
          isFollowing: true,
          followerCount: 0,
          followingCount: 0
        });
        console.log('Added user to following data, new size:', newData.size);
        return newData;
      });
      
      // Then reload the full data
      await loadFollowingData();
    } catch (error) {
      console.error('Failed to follow user:', error);
      throw error;
    }
  };

  const unfollowUser = async (userId: string) => {
    if (!session || status !== "authenticated") {
      console.warn('Cannot unfollow user: not authenticated');
      return;
    }
    try {
      const res = await fetch('/api/following', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });
      if (!res.ok) throw new Error('Failed to unfollow user');
      
      // Remove user from following data immediately for better UX
      setFollowingData(prev => {
        const newData = new Map(prev);
        newData.delete(userId);
        return newData;
      });
      
      // Then reload the full data
      await loadFollowingData();
    } catch (error) {
      console.error('Failed to unfollow user:', error);
      throw error;
    }
  };

  const toggleFollow = async (userId: string) => {
    const current = followingData.get(userId);
    if (current?.isFollowing) {
      await unfollowUser(userId);
    } else {
      await followUser(userId);
    }
  };

  const getFollowingStatus = (userId: string): FollowingRelationship | null => {
    return followingData.get(userId) || null;
  };

  const refreshFollowingData = async () => {
    await loadFollowingData();
  };

  return {
    followingData,
    isLoading,
    followUser,
    unfollowUser,
    toggleFollow,
    getFollowingStatus,
    refreshFollowingData
  };
}

// Hook for getting following statistics
export function useFollowingStats(userId?: string) {
  const { followingData } = useFollowing();
  const [stats, setStats] = useState({
    totalFollowing: 0,
    totalFollowers: 0,
    mutualFollows: 0
  });

  useEffect(() => {
    const following = Array.from(followingData.values()).filter(item => item.isFollowing);
    const totalFollowing = following.length;
    const totalFollowers = Array.from(followingData.values()).reduce(
      (sum, item) => sum + (item.isFollowing ? 1 : 0), 0
    );

    setStats({
      totalFollowing,
      totalFollowers,
      mutualFollows: 0 // This would be calculated based on mutual relationships
    });
  }, [followingData]);

  return stats;
}
