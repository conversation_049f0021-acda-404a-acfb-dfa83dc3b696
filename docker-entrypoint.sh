#!/bin/sh

# Docker entrypoint script for the forum application
set -e

echo "🚀 Starting Forum Application..."

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
until nc -z postgres 5432; do
  echo "Database is unavailable - sleeping"
  sleep 2
done
echo "✅ Database is ready!"

# Sync database schema
echo "🔄 Syncing database schema..."
npx prisma db push

# Seed the database if it's empty
echo "🌱 Seeding database with initial data..."
npx prisma db seed || echo "⚠️ Database seeding failed or already seeded"

echo "🎉 Application is ready to start!"

# Execute the main command
exec "$@"
