const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

console.log('Available models:', Object.keys(prisma));
console.log('Has notification:', 'notification' in prisma);

// Test connecting
async function test() {
  try {
    await prisma.$connect();
    console.log('Connected to database');
    
    // Check if we can access notification table
    if (prisma.notification) {
      console.log('notification model is available');
    } else {
      console.log('notification model is NOT available');
    }
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

test();
