# COMFOR - Communities Forum

A modern, full-stack forum application built with Next.js 15, featuring role-based access control, real-time interactions, and a beautiful UI.

## 🚀 Features

### Core Functionality
- **User Authentication**: Secure login/logout with NextAuth.js
- **Role-Based Access Control**: Admin, Editor, and User roles with different permissions
- **Post Management**: Create, read, update, and delete posts with rich content
- **Comment System**: Nested comments with real-time interactions
- **Community System**: Create and join communities with dedicated spaces
- **Like System**: Like posts and comments with real-time updates
- **Search & Filtering**: Advanced search and filtering capabilities
- **User Profiles**: Comprehensive user profiles with activity tracking

### Technical Features
- **Modern Stack**: Next.js 15 with App Router, TypeScript, Prisma ORM
- **Database**: PostgreSQL with Prisma for type-safe database operations
- **Styling**: Tailwind CSS with DaisyUI for beautiful, responsive design
- **Authentication**: NextAuth.js with GitHub OAuth integration
- **API**: RESTful API with proper error handling and validation
- **Performance**: Optimized with caching and efficient queries
- **Real-Time Layer**: Socket.IO (basic broadcast + user rooms) for extensible live updates
- **Error Handling**: Comprehensive error boundaries and user feedback

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, DaisyUI
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js
- **Deployment**: Vercel-ready

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd forum-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Copy the example file (only template kept in repo):
   ```bash
   cp .env.example .env
   ```

   (Optional) Generate production env via script (writes `.env.production`):
   ```bash
   npm run env:gen:prod
   ```

   Then edit `.env` (local only – do NOT commit) and fill in your variables:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/forum_db"
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key"
   GITHUB_CLIENT_ID="your-github-oauth-id"
   GITHUB_CLIENT_SECRET="your-github-oauth-secret"
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   npx prisma db seed
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

📚 **For detailed Docker documentation, see [DOCKER.md](./DOCKER.md)**

## Features

- **Role-Based Access Control**: Admin, Editor, and User roles with different permissions
- **Authentication**: NextAuth.js with credentials and OAuth providers (Google, GitHub)
- **Posts & Comments**: Create, edit, and manage forum posts with nested comments
- **Likes & Engagement**: Like posts and comments with real-time counters
- **Sharing**: Social media sharing and copy-to-clipboard functionality
- **Admin Dashboard**: Comprehensive admin panel for user and content management
- **Responsive Design**: Built with Tailwind CSS and DaisyUI components
- **Type Safety**: Full TypeScript implementation

## Tech Stack

- **Frontend**: Next.js 15 (App Router), TypeScript, Tailwind CSS, DaisyUI
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL (configurable)
- **Authentication**: NextAuth.js
- **State Management**: Zustand
- **Forms**: React Hook Form with Zod validation
- **Rich Text**: Tiptap editor (ready for implementation)
- **Containerization**: Docker & Docker Compose
- **Caching**: Redis (included in Docker setup)
- **Reverse Proxy**: Nginx (production setup)

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database (or use SQLite for development)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd forum-app
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables (same as earlier section):
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/forum_db?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers (optional – enable only what you configure)
GOOGLE_CLIENT_ID=""      # if using Google
GOOGLE_CLIENT_SECRET=""  # if using Google
GITHUB_CLIENT_ID=""      # if using GitHub
GITHUB_CLIENT_SECRET=""  # if using GitHub
```

4. Set up the database:
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (for development)
npm run db:push

# Or run migrations (for production)
npm run db:migrate
```

5. Start the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Secret Rotation & Safety

If a secret may be exposed (committed earlier, shared, leaked), rotate immediately:

1. Generate new secret: `openssl rand -base64 48` (or a password manager).
2. Update hosting provider / secret manager first.
3. Update local `.env` (never commit real secrets) & redeploy / restart.
4. Rotate `NEXTAUTH_SECRET`: invalidate sessions (truncate `Session` table or change `SESSION_COOKIE_NAME`).
5. Rotate DB password: create new user or ALTER ROLE, update `DATABASE_URL`, deploy, then revoke old credentials.
6. Commit only changes to `.env.example` if variable names changed.

Previously committed environment files `.env` / `.env.dev` have been removed; treat those secrets as compromised and rotate them.

## Demo Accounts

For testing purposes, you can use these demo accounts:

- **Admin**: <EMAIL> (any password)
- **Editor**: <EMAIL> (any password)
- **User**: <EMAIL> (any password)

*Note: In demo mode, password validation is disabled for testing.*

## Project Structure

```
forum-app/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── admin/          # Admin dashboard
│   │   ├── api/            # API routes
│   │   ├── auth/           # Authentication pages
│   │   └── posts/          # Forum posts pages
│   ├── components/         # Reusable components
│   └── lib/               # Utilities and configurations
├── prisma/                # Database schema and migrations
└── public/               # Static assets
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

## User Roles & Permissions

### Admin
- Full access to all features
- User management and role assignment
- Content moderation
- Forum settings and configuration

### Editor
- Create and edit posts
- Moderate comments
- Access to editor tools

### User
- Create posts and comments
- Like and share content
- Basic profile management

## API Routes

- `POST /api/auth/[...nextauth]` - NextAuth.js authentication
- `GET/POST /api/posts` - Posts management
- `GET/POST /api/comments` - Comments management
- `POST /api/likes` - Like/unlike functionality
- `GET/POST /api/users` - User management (admin only)

## Database Schema

The application uses Prisma with the following main models:

- **User**: User accounts with roles
- **Post**: Forum posts
- **Comment**: Nested comments
- **Like**: Likes for posts and comments
- **Account/Session**: NextAuth.js tables

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## 🎯 Key Features Implemented

### Dynamic Content Loading
- Real-time data fetching from PostgreSQL database
- Efficient pagination and filtering
- Loading states and error handling

### User Experience
- Responsive design across all devices
- Smooth animations and transitions
- Intuitive navigation and user flows
- Comprehensive error handling

### Performance Optimizations
- Efficient database queries with Prisma
- Optimized API endpoints
- Client-side caching strategies
- Image optimization

### Security Features
### Real-Time Interactions

Initial real-time support is provided via a lightweight Socket.IO integration:

- Server singleton initialized under `api/socket/route.ts` (in-memory, best-effort in dev/Vercel single region)
- Client connection & event hooks via `RealtimeProvider`
- Events implemented:
   - `post:new` (broadcast to all other clients)
   - `notification` (targeted to `user:{userId}` room when `userId` provided, else broadcast)
- Automatic room join for authenticated users using `user:{id}` pattern

Limitations & Next Steps:
- No guaranteed delivery (stateless server instance / no persistence)
- Multi-region & horizontal scaling would require an adapter (e.g. Redis adapter) and external deployment (Node server or Edge-compatible alternative like Pusher/Ably)
- Consider migrating to a dedicated real-time service or adding Redis + `@socket.io/redis-adapter` for production scale

To emit events from server code (e.g. after creating a post), you can import the existing singleton by attaching it to `globalThis._io` (already done in the route). For API route handlers you may need a helper util to safely reference the instance.

- Role-based access control
- Secure authentication with NextAuth.js
- Protected API routes
- Input validation and sanitization

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - Vercel will automatically build and deploy

### Environment Variables for Production
```env
DATABASE_URL="your-production-database-url"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-production-secret"
GITHUB_CLIENT_ID="your-github-oauth-id"
GITHUB_CLIENT_SECRET="your-github-oauth-secret"
```

## 🎉 Demo

The application includes:
- **Sample Data**: Pre-seeded with users, posts, communities, and comments
- **Role Demonstration**: Different user roles with varying permissions
- **Interactive Features**: Full CRUD operations, likes, comments, and community management
- **Responsive Design**: Optimized for all screen sizes
- **Error Handling**: Comprehensive error pages and user feedback

Visit the live demo or run locally to explore all features!

## Support

For support and questions, please open an issue in the repository.

---

**Built with ❤️ using Next.js 15 and modern web technologies**

---

### Version Alignment

This documentation reflects the current framework/library versions specified in `package.json`:

- Next.js: 15.4.5
- React / React DOM: 19.1.0

If you upgrade these dependencies, update this section to keep the docs accurate.
