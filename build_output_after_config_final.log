
> forum-app@0.1.0 build
> next build

   ▲ Next.js 15.4.5 (Turbopack)
   - Environments: .env.local, .env

   Creating an optimized production build ...
/*! 🌼 daisyUI 5.0.50 */
 ✓ Compiled successfully in 5.0s
   Skipping linting
   Checking validity of types ...
   Collecting page data ...
   Generating static pages (0/47) ...
Error: <Html> should not be imported outside of pages/_document.
Read more: https://nextjs.org/docs/messages/no-document-import-in-page
    at x (.next/server/chunks/8548.js:6:1351)
Error occurred prerendering page "/404". Read more: https://nextjs.org/docs/messages/prerender-error
Error: <Html> should not be imported outside of pages/_document.
Read more: https://nextjs.org/docs/messages/no-document-import-in-page
    at x (.next/server/chunks/8548.js:6:1351)
Export encountered an error on /_error: /404, exiting the build.
 ⨯ Next.js build worker exited with code: 1 and signal: null
