

services:
  postgres:
    image: postgres:15-alpine
    container_name: forum-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: forum_db
      POSTGRES_USER: forum_user
      POSTGRES_PASSWORD: forum_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - forum-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U forum_user -d forum_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  forum-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: forum-app
    restart: unless-stopped
    environment:
      DATABASE_URL: ****************************************************/forum_db?schema=public
      NEXTAUTH_URL: http://localhost:3002
      NEXTAUTH_SECRET: your-super-secret-nextauth-secret-change-in-production
      NODE_ENV: production
      SEED_FILE: /app/prisma/seed-data.json
      SEED_RESET: "false"
      GOOGLE_CLIENT_ID: ""
      GOOGLE_CLIENT_SECRET: ""
      GITHUB_CLIENT_ID: ""
      GITHUB_CLIENT_SECRET: ""
    ports:
      - "3002:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - forum-network
    volumes:
      - uploads_data:/app/public/uploads

  redis:
    image: redis:7-alpine
    container_name: forum-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - forum-network
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

volumes:
  postgres_data:
  redis_data:
  uploads_data:

networks:
  forum-network:
