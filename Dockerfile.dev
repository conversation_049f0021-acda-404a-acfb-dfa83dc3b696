# Development Dockerfile for hot reloading
FROM node:18-alpine

# Install dependencies for development
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Create uploads directory
RUN mkdir -p public/uploads

# Expose port
EXPOSE 3000

# Development command with hot reloading
CMD ["npm", "run", "dev"]
