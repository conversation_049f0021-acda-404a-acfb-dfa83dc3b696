const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugFollows() {
  try {
    console.log('=== DEBUGGING FOLLOW RELATIONSHIPS ===\n');
    
    // Get all users
    const users = await prisma.user.findMany({
      select: { 
        id: true, 
        name: true, 
        email: true,
        _count: {
          select: {
            followers: true,
            following: true
          }
        }
      }
    });
    
    console.log('USERS:');
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email}): ${user._count.followers} followers, ${user._count.following} following`);
    });
    
    // Get all follow relationships
    const follows = await prisma.follow.findMany({
      include: {
        follower: { select: { name: true, email: true } },
        following: { select: { name: true, email: true } }
      }
    });
    
    console.log('\nFOLLOW RELATIONSHIPS:');
    if (follows.length === 0) {
      console.log('No follow relationships found');
    } else {
      follows.forEach(follow => {
        console.log(`- ${follow.follower.name} follows ${follow.following.name}`);
      });
    }
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    await prisma.$disconnect();
  }
}

debugFollows();
