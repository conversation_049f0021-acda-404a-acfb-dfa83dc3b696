/** Tailwind CSS v4 optional config file.
 * DaisyUI v5 configuration lives in CSS via @plugin "daisyui" { ... } blocks (see globals.css).
 * Do NOT add a legacy `daisyui` key here; that was v3 style and will cause TS errors.
 * Add/adjust content globs if you introduce new template locations.
 * Docs: https://tailwindcss.com & https://daisyui.com/docs/config/
 */

/** @type {import('tailwindcss').Config} */
const tailwindConfig = {
  content: [
    './src/**/*.{js,jsx,ts,tsx,mdx}',
    './server/**/*.{ts,js}',
    './prisma/**/*.{ts,js}',
  ],
  // No theme/plugins needed here; using CSS-first plugin configuration approach.
};

export default tailwindConfig;
