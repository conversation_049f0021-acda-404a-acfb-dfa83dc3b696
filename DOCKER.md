# Docker Setup for Forum Application

This guide explains how to run the Forum Application using Docker containers.

## Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)
- Git

## Quick Start

1. **Clone the repository and navigate to the forum-app directory:**
   ```bash
   git clone <repository-url>
   cd forum-app
   ```

2. **Start the application with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

3. **Wait for the services to start (about 30-60 seconds):**
   ```bash
   # Check status
   docker-compose ps

   # Check logs
   docker-compose logs forum-app
   ```

4. **Access the application:**
   - Forum: http://localhost:3000
   - Health Check: http://localhost:3000/api/health
   - Database: localhost:5432
   - Redis: localhost:6379

5. **Stop the application:**
   ```bash
   docker-compose down
   ```

That's it! The application should be running with a PostgreSQL database.

## Demo Accounts

After the database is seeded, you can use these demo accounts:

- **Admin User**: <EMAIL>
- **Editor User**: <EMAIL>
- **Regular User**: <EMAIL>

*Note: Authentication is handled by NextAuth.js. The actual login process depends on your configured providers.*

## Docker Services

The Docker setup includes the following services:

### Production Services (`docker-compose.yml`)

- **forum-app**: Next.js application (port 3000)
- **postgres**: PostgreSQL database (port 5432)
- **redis**: Redis cache (port 6379)

### Development Services (`docker-compose.dev.yml`)

- **forum-app-dev**: Next.js with hot reloading (port 3000)
- **postgres**: PostgreSQL database (port 5433)
- **redis-dev**: Redis cache (port 6380)

## Environment Configuration

### Production Environment

Copy the Docker environment template:
```bash
cp .env.docker .env.local
```

Edit `.env.local` with your configuration:
```env
# Database
DATABASE_URL="****************************************************/forum_db?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-super-secret-nextauth-secret-change-in-production"

# OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
```

## Available Commands

### Production Commands

```bash
# Start all services
npm run docker:up

# Stop all services
npm run docker:down

# View logs
npm run docker:logs

# Build the application image
npm run docker:build

# Clean up (remove containers, volumes, and unused images)
npm run docker:clean
```

### Development Commands

```bash
# Start development services with hot reloading
npm run docker:up:dev

# Stop development services
npm run docker:down:dev

# Build development image
npm run docker:build:dev
```

### Manual Docker Commands

```bash
# Build and start services
docker-compose up --build -d

# Start only specific services
docker-compose up postgres redis -d

# View service logs
docker-compose logs -f forum-app

# Execute commands in running container
docker-compose exec forum-app sh

# Run database migrations
docker-compose exec forum-app npx prisma migrate deploy

# Access PostgreSQL
docker-compose exec postgres psql -U forum_user -d forum_db
```

## Development Workflow

### For Development with Hot Reloading

1. **Start development services:**
   ```bash
   npm run docker:up:dev
   ```

2. **The application will be available at:**
   ```
   http://localhost:3000
   ```

3. **Make changes to your code** - they will be automatically reflected due to volume mounting.

4. **Access the database:**
   ```bash
   docker-compose -f docker-compose.dev.yml exec postgres psql -U forum_user -d forum_db_dev
   ```

### For Production Testing

1. **Start production services:**
   ```bash
   npm run docker:up
   ```

2. **The application will be available at:**
   ```
   http://localhost:3131
   ```

## Database Management

### Running Migrations

```bash
# In production
docker-compose exec forum-app npx prisma migrate deploy

# In development
docker-compose -f docker-compose.dev.yml exec forum-app-dev npx prisma migrate dev
```

### Accessing Database

```bash
# Production database
docker-compose exec postgres psql -U forum_user -d forum_db

# Development database
docker-compose -f docker-compose.dev.yml exec postgres psql -U forum_user -d forum_db_dev
```

### Database Backup and Restore

```bash
# Backup
docker-compose exec postgres pg_dump -U forum_user forum_db > backup.sql

# Restore
docker-compose exec -T postgres psql -U forum_user -d forum_db < backup.sql
```

## Troubleshooting

### Common Issues

1. **Port conflicts:**
   - Production uses ports 3131, 5432, 6379 (app exposed on 3131)
   - Development uses ports 3000, 5433, 6380
   - Make sure these ports are available

2. **Database seeding fails:**
   ```bash
   # If seeding fails, run migrations and generate Prisma client manually:
   docker-compose exec forum-app npx prisma migrate deploy
   docker-compose exec forum-app npx prisma generate
   docker-compose restart forum-app
   ```

3. **Health check returns 500 error:**
   ```bash
   # Restart the application container to reload Prisma client:
   docker-compose restart forum-app

   # Then test the health endpoint:
   curl http://localhost:3000/api/health
   ```

4. **Database connection issues:**
   ```bash
   # Check if database is ready
   docker-compose exec postgres pg_isready -U forum_user -d forum_db
   
   # View database logs
   docker-compose logs postgres
   ```

3. **Application won't start:**
   ```bash
   # Check application logs
   docker-compose logs forum-app
   
   # Rebuild the image
   docker-compose up --build
   ```

4. **Permission issues:**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER .
   ```

### Cleaning Up

```bash
# Remove all containers and volumes
npm run docker:clean

# Or manually:
docker-compose down -v --remove-orphans
docker system prune -f
docker volume prune -f
```

## File Structure

```
forum-app/
├── Dockerfile              # Production Dockerfile
├── Dockerfile.dev          # Development Dockerfile
├── docker-compose.yml      # Production services
├── docker-compose.dev.yml  # Development services
├── docker-entrypoint.sh    # Application startup script
├── .dockerignore           # Files to exclude from Docker build
├── .env.docker             # Docker environment template
├── init-db.sql             # Database initialization script
└── DOCKER.md               # This documentation
```

## Security Notes

- Change default passwords in production
- Use strong secrets for NEXTAUTH_SECRET
- Configure OAuth providers properly
- Use environment-specific configurations
- Regular security updates for base images

## Performance Tips

- Use multi-stage builds (already implemented)
- Optimize Docker layer caching
- Use .dockerignore to reduce build context
- Monitor resource usage with `docker stats`
- Use health checks for better reliability
